{"app": {"name": "NSL", "version": "Version {version}"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "submit": "Submit", "required": "Required", "optional": "Optional", "search": "Search", "filter": "Filter", "sort": "Sort", "view": "View", "create": "Create", "update": "Update", "details": "Details", "noData": "No data available", "retry": "Retry", "ok": "OK", "enterValue": "Enter value", "pageNotFound": "Page Not Found", "pageNotFoundMessage": "The page {pageName} was not found.", "start": "Start", "notAvailable": "N/A"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "signUp": "Sign Up", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "User Name", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "organization": "Organization", "role": "Role", "mobile": "Mobile", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "welcomeBack": "Welcome Back", "pleaseSignIn": "Please sign in to continue", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "resetPassword": "Reset Password", "signIn": "Sign In", "alreadyRegistered": "Already Registered? Click", "notRegistered": "Not Registered? Click", "profilePicture": "Profile Picture", "uploadProfilePicture": "Upload Profile Picture", "registrationSuccess": "Your account has been created successfully. Please login with your credentials.", "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "usernameRequired": "Username is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "organizationRequired": "Organization is required", "roleRequired": "Role is required", "mobileRequired": "Mobile number is required", "mobileInvalid": "Please enter a valid mobile number"}, "localAccount": "Local Account", "loggingOut": "Logging out...", "signOutOfAccount": "Sign out of your account"}, "navigation": {"home": "Home", "chat": "Cha<PERSON>", "build": "Create", "transact": "Transaction", "myTransactions": "My Transactions", "settings": "Settings", "profile": "Profile", "logout": "Logout", "dashboard": "Dashboard", "components": "Components", "uiComponents": "UI Components", "drawer": {"appName": "NSL"}, "help": "Help", "logoutConfirmation": "Are you sure you want to logout?", "helpComingSoon": "Help section coming soon!", "widgetBinder": "Widget Binder", "code": "Code"}, "settings": {"title": "Settings", "theme": "Theme", "darkMode": "Dark Mode", "darkModeDescription": "Toggle between light and dark theme", "language": "Language", "languageDescription": "Change the application language", "notifications": "Notifications", "account": "Account", "about": "About", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "saveSuccess": "Setting<PERSON> saved successfully", "saveChanges": "Save Changes", "appearance": "Appearance", "privacy": "Privacy", "advanced": "Advanced", "uiSettings": "UI Settings", "chatSettings": "<PERSON><PERSON>", "fontSize": "Font Size", "fontSizeDescription": "Adjust the size of text throughout the application", "uiDensity": "UI Density", "uiDensityDescription": "Adjust the spacing between UI elements", "showTimestamps": "Show Timestamps", "showTimestampsDescription": "Display timestamps for each message", "showReadReceipts": "Show Read Receipts", "showReadReceiptsDescription": "Let others know when you've read their messages", "sendMessageOnEnter": "Send Message on Enter", "sendMessageOnEnterDescription": "Press Enter to send messages instead of Shift+Enter", "version": "Version", "versionDescription": "Current application version", "light": "Light", "dark": "Dark", "system": "System", "comingSoon": "{feature} settings coming soon"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "changePassword": "Change Password", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "userInfoNotAvailable": "User information not available", "profileInformation": "Profile Information", "accountInformation": "Account Information", "refreshProfile": "Refresh Profile", "fullName": "Full Name", "username": "Username", "emailAddress": "Email Address", "mobileNumber": "Mobile Number", "role": "Role", "organization": "Organization", "userId": "User ID", "accountStatus": "Account Status", "tenantId": "Tenant ID", "roles": "Roles", "organizationUnits": "Organization Units", "authProvider": "Authentication Provider", "notProvided": "Not provided", "notAvailable": "Not available", "editProfileComingSoon": "Edit profile functionality coming soon", "viewProfileDetails": "View your profile details"}, "chat": {"newChat": "New Chat", "newConversation": "New Conversation", "typeMessage": "Type a message...", "send": "Send", "greeting": "How can I help you {greeting}?", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "cancel": "Cancel", "clear": "Clear", "chatWithNSL": "Chat with NSL...", "fetchingAnswer": "NSL fetching answer", "conversations": "Conversations", "history": "History", "searchConversations": "Search conversations", "recentChats": "Recent Chats", "chatCount": "{count} chats", "noConversations": "No conversations yet", "noMessagesYet": "No messages yet", "rename": "<PERSON><PERSON>", "renameConversation": "Rename Conversation", "enterNewName": "Enter new name", "exportChat": "Export <PERSON>", "context": "Context", "aiAssistant": "AI Assistant", "currentConversation": "Current Conversation", "topic": "Topic:", "noSpecificTopic": "No specific topic detected", "keyPoints": "Key Points:", "noContextAvailable": "• No context available for this conversation", "relatedInformation": "Related Information", "noRelatedInformation": "No related information available", "microphonePermissionRequired": "Microphone Permission Required", "microphonePermissionMessage": "This app needs microphone access for text-to-speech functionality. Please grant microphone permission in your device settings.", "suggestions": {"codeReview": "Help me with code review", "explainConcept": "Explain this concept", "debugCode": "Debug my code"}}, "build": {"newSolution": "New Solution", "solutionName": "Solution Name", "description": "Description", "uploadFile": "Upload File", "createSolution": "Create Solution", "create": "Create", "newProject": "New Project", "backToCreateMenu": "Back to create menu", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "noSolutions": "No solutions available", "createYourFirstSolution": "Create your first solution", "solutions": "Solutions", "solution": "Solution", "solutionDetails": "Solution Details", "exportYAML": "Export YAML", "rolesGreeting": "Great! Here is your Roles and it's use cases for this solution.", "entitiesGreeting": "Great! Here is your Entities and it's use cases for this solution.", "workflowGreeting": "Great! Here is your Workflow and it's use cases for this solution.", "suggestions": {"title": "Try asking NSL to:", "createWorkflow": "Create a workflow", "generateYAML": "Generate YAML", "buildSolution": "Build a solution"}, "examples": {"createWorkflow": "Create a workflow for processing customer orders", "generateYAML": "Generate YAML for a data transformation pipeline", "buildSolution": "Build a solution for inventory management"}}, "workflow": {"title": "Workflows", "createNew": "Create New Workflow", "createNewComingSoon": "Create new workflow - Coming soon", "workflowDetails": "Workflow Details", "workflowDetailsComingSoon": "Workflow details - Coming soon", "status": {"active": "Active", "draft": "Draft", "archived": "Archived", "completed": "Completed"}, "types": {"approvalWorkflow": "Approval Workflow", "documentProcessing": "Document Processing"}, "descriptions": {"approvalWorkflow": "Standard approval process with multiple steps", "documentProcessing": "Automated document processing and validation"}, "nodeDetails": "Node Details", "closeDetails": "Close details", "currentSolution": "Current Solution", "noActiveSolution": "No active solution", "components": "Components", "noComponentsDefined": "No components defined yet", "solutionTemplates": "Solution Templates", "templates": {"dataProcessing": "Data Processing Pipeline", "orderManagement": "Order Management System", "customerFeedback": "Customer Feedback Analysis"}}, "components": {"title": "UI Components", "richTextEditor": "Quill Rich Text Editor", "basicEditor": "Basic Editor with <PERSON><PERSON><PERSON>", "tryFormatting": "Try formatting this text using the toolbar above.", "startTyping": "Start typing here..."}, "dashboard": {"noNotifications": "No Notificataions", "noRecentActivity": "No recent activity", "notifications": "Notifications", "quickActions": "<PERSON><PERSON><PERSON><PERSON>", "recentActivity": "Recent Activity", "statistics": "Statistics", "title": "Dashboard", "viewAll": "View All", "welcome": "Welcome, {name}!"}, "transaction": {"globalObjectives": "Global Objectives", "localObjectives": "Local Objectives", "status": "Status", "id": "ID", "version": "Version", "viewDetails": "View Details", "startWorkflow": "Start Workflow", "completeWorkflow": "Complete Workflow", "noObjectives": "No objectives available", "transact": "Transact", "myTransactions": "My Transactions", "checkingExistingTransactions": "Checking for existing transactions...", "existingTransactions": "Existing Transactions", "existingTransactionsFor": "Existing transactions for {name}", "existingTransactionsQuestion": "There are existing transactions. Do you want to see them?", "noExistingTransactions": "No existing transactions found", "startNewTransactionPrompt": "Start a new transaction using the button below", "startNewTransaction": "Start New Transaction", "instanceId": "Instance ID", "created": "Created", "updated": "Updated", "resume": "Resume", "yes": "Yes", "no": "No", "data": "Data", "timestamp": "Timestamp", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "completed": "COMPLETED", "pending": "PENDING", "failed": "FAILED", "greeting": "Hello, {name}", "welcomeMessage": "Welcome to the Transaction Center", "searchTransactions": "Search transactions...", "enterTransactionDetails": "Enter transaction details...", "errorLoadingData": "Error loading data: {error}", "objectiveId": "ID: {id}", "objectiveName": "Name: {name}", "objectiveStatus": "Status: {status}", "objectiveVersion": "Version: {version}", "objectiveDetails": "Objective Details", "startTransaction": "Start Transaction", "startNewTransactionWith": "Start a new transaction with {name}?", "errorLoadingTransactions": "Error loading transactions", "loadingTransactions": "Loading transactions...", "noTransactionsFound": "No transactions found", "noFilteredTransactionsFound": "No {status} transactions found", "all": "All", "newTransactionMessage": "New transaction form would go here.", "editTransactionMessage": "Edit transaction functionality would go here.", "resumeTransactionMessage": "This would navigate to the workflow detail screen to resume the transaction.", "workerId": "Worker ID: {id}", "workflowId": "Workflow ID", "dateTime": "Date & Time", "lastUpdated": "Last Updated", "updatedDate": "Updated: {date}", "formattedDate": "{date}", "totalLocalObjectives": "Total Local Objectives: {count}", "noLocalObjectiveDetails": "No local objective details available", "localObjective": "Local Objective", "loCount": "{count} LO{plural}", "selectItemForDetails": "Select an item to view details", "globalObjectiveDetails": "Global Objective Details", "transactionDetails": "Transaction Details", "groupedTransactionDetails": "Grouped Transaction Details", "tenantId": "Tenant ID", "resumeTransaction": "Resume Transaction"}, "home": {"typeYourReply": "Type your reply", "greeting": "Hi {name}, How can I help you?", "selectQuickMessage": "Please select a quick message type before sending", "selectQuickMessageHint": "Select a message type", "sendingMessage": "Sending message...", "loadingChatHistory": "Loading chat history...", "noChatHistory": "No chat history found", "askNSL": "Ask NSL", "nsl": "NSL", "solution": "Solution", "general": "General", "internet": "Internet"}, "library": {"books": "12 Books", "objects": "102 Objects", "solutions": "35 Solutions", "pageTitle": "My Library", "createButtonText": "Create Book"}, "sidemenu": {"chat": "Cha<PERSON>", "create": "Create", "myBusiness": "My Business", "home": "Home", "collections": "Collections", "solutions": "Solutions", "records": "Records", "myTransactions": "My Transactions", "calendar": "Calendar", "notifications": "Notifications", "nslToJavaCode": "NSL to Java Code", "myProfile": "My Profile", "maxNewPlan": "<PERSON> Plan", "viewPlan": "View Plan", "learnMore": "Learn More", "language": "Language", "getHelp": "Get Help", "settings": "Settings", "logout": "Logout"}, "bookdetails": {"createYourBook": "Create Your Book", "name": "Name", "description": "Description", "industry": "Industry", "descriptionAboutTheProject": "Description about the project", "start": "Start"}, "websolution": {"books": "12 Books", "objects": "102 Objects", "solutions": "35 Solutions", "pageTitle": "My Solution", "createButtonText": "Create Solution"}, "webobject": {"pageTitle": "My Objects", "createButtonText": "Create Object"}, "myBusinessHome": {"favourites": "Favourites", "recentUsedSolutions": "Recent Used Solutions"}, "myBusinessCollections": {"collections": "Collections"}, "myBusinessSolutions": {"solutions": "Solutions", "start": "Start", "pending": "Pending"}}