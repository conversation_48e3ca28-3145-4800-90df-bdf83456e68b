{"local_objective": "lo001", "user_inputs": [{"input_id": "in001", "input_stack_id": 1001, "attribute_id": "at001", "entity_id": "e001", "display_name": "Audio Auto", "data_type": "Audio", "source_type": "user", "required": true, "ui_control": "oj-audio-auto", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in001", "input_value": "https://example.com/sample-audio.mp3", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Automatic audio control", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in002", "input_stack_id": 1001, "attribute_id": "at002", "entity_id": "e001", "display_name": "Audio Recording", "data_type": "Audio", "source_type": "user", "required": true, "ui_control": "oj-audio-recording", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in002", "input_value": {"recording_id": "rec_78901", "duration": 90, "format": "mp3", "url": "https://storage.example.com/recordings/rec_78901.mp3"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Record audio input", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in003", "input_stack_id": 1001, "attribute_id": "at003", "entity_id": "e001", "display_name": "Slide<PERSON>", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-slider", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100, "step": 1}, "contextual_id": "go001.lo001.in003", "input_value": 50, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select a value using slider", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in004", "input_stack_id": 1001, "attribute_id": "at004", "entity_id": "e001", "display_name": "Boolean", "data_type": "Boolean", "source_type": "user", "required": true, "ui_control": "oj-switch", "is_visible": true, "allowed_values": [true, false], "validations": null, "contextual_id": "go001.lo001.in004", "input_value": true, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Toggle between true/false", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in005", "input_stack_id": 1001, "attribute_id": "at005", "entity_id": "e001", "display_name": "Byte", "data_type": "Byte", "source_type": "user", "required": true, "ui_control": "oj-input-number", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 255}, "contextual_id": "go001.lo001.in005", "input_value": 128, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter byte value (0-255)", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in006", "input_stack_id": 1001, "attribute_id": "at006", "entity_id": "e001", "display_name": "Capture Image", "data_type": "Image", "source_type": "user", "required": true, "ui_control": "oj-capture-image", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in006", "input_value": {"image_id": "img_12345", "timestamp": "2025-05-06T14:30:00", "format": "jpeg", "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAgGBgcGBQg..."}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Capture image from camera", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in007", "input_stack_id": 1001, "attribute_id": "at007", "entity_id": "e001", "display_name": "Checkbox", "data_type": "Boolean", "source_type": "user", "required": true, "ui_control": "oj-checkbox", "is_visible": true, "allowed_values": [true, false], "validations": null, "contextual_id": "go001.lo001.in007", "input_value": true, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Check to confirm", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in008", "input_stack_id": 1001, "attribute_id": "at008", "entity_id": "e001", "display_name": "Chip Multi", "data_type": "Array", "source_type": "user", "required": true, "ui_control": "oj-chip-multi", "is_visible": true, "allowed_values": ["Red", "Green", "Blue", "Yellow"], "validations": null, "contextual_id": "go001.lo001.in008", "input_value": ["Red", "Blue"], "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select multiple options", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Red", "label": "Red"}, {"value": "Green", "label": "Green"}, {"value": "Blue", "label": "Blue"}, {"value": "Yellow", "label": "Yellow"}]}, {"input_id": "in009", "input_stack_id": 1001, "attribute_id": "at009", "entity_id": "e001", "display_name": "<PERSON>", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-chip-single", "is_visible": true, "allowed_values": ["Red", "Green", "Blue", "Yellow"], "validations": null, "contextual_id": "go001.lo001.in009", "input_value": "Green", "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select one option", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Red", "label": "Red"}, {"value": "Green", "label": "Green"}, {"value": "Blue", "label": "Blue"}, {"value": "Yellow", "label": "Yellow"}]}, {"input_id": "in010", "input_stack_id": 1001, "attribute_id": "at010", "entity_id": "e001", "display_name": "Current Date Current Date Only", "data_type": "Date", "source_type": "system", "required": true, "ui_control": "oj-current-date-only", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in010", "input_value": "2025-05-06", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display current date", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in011", "input_stack_id": 1001, "attribute_id": "at011", "entity_id": "e001", "display_name": "Current Time Only", "data_type": "Time", "source_type": "system", "required": true, "ui_control": "oj-current-time-only", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in011", "input_value": "14:30:00", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display current time", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in012", "input_stack_id": 1001, "attribute_id": "at012", "entity_id": "e001", "display_name": "Current Date, Time", "data_type": "DateTime", "source_type": "system", "required": true, "ui_control": "oj-current-date-time", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in012", "input_value": "2025-05-06T14:30:00", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display current date and time", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in013", "input_stack_id": 1001, "attribute_id": "at013", "entity_id": "e001", "display_name": "Date", "data_type": "Date", "source_type": "user", "required": true, "ui_control": "oj-input-date", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in013", "input_value": "2025-06-15", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select a date", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in014", "input_stack_id": 1001, "attribute_id": "at014", "entity_id": "e001", "display_name": "Date Range", "data_type": "DateRange", "source_type": "user", "required": true, "ui_control": "oj-date-range", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in014", "input_value": {"start_date": "2025-05-10", "end_date": "2025-05-20"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select a date range", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in015", "input_stack_id": 1001, "attribute_id": "at015", "entity_id": "e001", "display_name": "Date Only", "data_type": "Date", "source_type": "user", "required": true, "ui_control": "oj-date-only", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in015", "input_value": "2025-07-04", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select a date without time", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in016", "input_stack_id": 1001, "attribute_id": "at016", "entity_id": "e001", "display_name": "Date Time", "data_type": "DateTime", "source_type": "user", "required": true, "ui_control": "oj-input-date-time", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in016", "input_value": "2025-08-15T09:30:00", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select date with time", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in017", "input_stack_id": 1001, "attribute_id": "at017", "entity_id": "e001", "display_name": "Decimal", "data_type": "Decimal", "source_type": "user", "required": true, "ui_control": "oj-input-number", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100, "step": 0.1}, "contextual_id": "go001.lo001.in017", "input_value": 42.5, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter decimal value", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in018", "input_stack_id": 1001, "attribute_id": "at018", "entity_id": "e001", "display_name": "Distance", "data_type": "Decimal", "source_type": "user", "required": true, "ui_control": "oj-distance", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "unit": "km"}, "contextual_id": "go001.lo001.in018", "input_value": {"value": 15.6, "unit": "km"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter distance in km", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in019", "input_stack_id": 1001, "attribute_id": "at019", "entity_id": "e001", "display_name": "Dropdown", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-select-one", "is_visible": true, "allowed_values": ["Option 1", "Option 2", "Option 3"], "validations": null, "contextual_id": "go001.lo001.in019", "input_value": "Option 2", "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select one option", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Option 1", "label": "Option 1"}, {"value": "Option 2", "label": "Option 2"}, {"value": "Option 3", "label": "Option 3"}]}, {"input_id": "in020", "input_stack_id": 1001, "attribute_id": "at020", "entity_id": "e001", "display_name": "Duration", "data_type": "Duration", "source_type": "user", "required": true, "ui_control": "oj-duration", "is_visible": true, "allowed_values": null, "validations": {"min": "PT0H", "max": "PT8H"}, "contextual_id": "go001.lo001.in020", "input_value": "PT3H30M", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter time duration", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in021", "input_stack_id": 1001, "attribute_id": "at021", "entity_id": "e001", "display_name": "Email", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-input-email", "is_visible": true, "allowed_values": null, "validations": {"pattern": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"}, "contextual_id": "go001.lo001.in021", "input_value": "<EMAIL>", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter email address", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in022", "input_stack_id": 1001, "attribute_id": "at022", "entity_id": "e001", "display_name": "Encrypt Text", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-encrypt-text", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in022", "input_value": {"plaintext": "Sensitive information", "encrypted": "uHj7PqRs2tYxVwEzA9B8C7D6E5F4G3H2I1J0kLmNoP"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter text to be encrypted", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in023", "input_stack_id": 1001, "attribute_id": "at023", "entity_id": "e001", "display_name": "File", "data_type": "File", "source_type": "user", "required": true, "ui_control": "oj-file", "is_visible": true, "allowed_values": null, "validations": {"max_size": 5000000, "extensions": [".pdf", ".doc", ".docx"]}, "contextual_id": "go001.lo001.in023", "input_value": {"filename": "report.pdf", "filesize": 2345678, "filetype": "application/pdf", "file_reference": "f8a72bc5d9e1", "upload_date": "2025-05-02T10:15:00"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Upload a file", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in024", "input_stack_id": 1001, "attribute_id": "at024", "entity_id": "e001", "display_name": "File IRDR", "data_type": "File", "source_type": "user", "required": true, "ui_control": "oj-file-irdr", "is_visible": true, "allowed_values": null, "validations": {"max_size": 10000000, "extensions": [".jpg", ".png", ".pdf"]}, "contextual_id": "go001.lo001.in024", "input_value": {"filename": "document_scan.pdf", "filesize": 3456789, "filetype": "application/pdf", "file_reference": "irdr_9876abc", "upload_date": "2025-05-03T11:20:00", "irdr_metadata": {"document_type": "invoice", "processed": true, "extraction_status": "complete"}}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Upload a file with IRDR", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in025", "input_stack_id": 1001, "attribute_id": "at025", "entity_id": "e001", "display_name": "HTML", "data_type": "HTML", "source_type": "user", "required": true, "ui_control": "oj-html", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in025", "input_value": "<p>This is <strong>HTML</strong> content</p><ul><li>Item 1</li><li>Item 2</li></ul>", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter HTML content", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in026", "input_stack_id": 1001, "attribute_id": "at026", "entity_id": "e001", "display_name": "Hyperlink", "data_type": "URL", "source_type": "user", "required": true, "ui_control": "oj-hyperlink", "is_visible": true, "allowed_values": null, "validations": {"pattern": "^https?:\\/\\/[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$"}, "contextual_id": "go001.lo001.in026", "input_value": "https://example.com/resources/page", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter a URL", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in027", "input_stack_id": 1001, "attribute_id": "at027", "entity_id": "e001", "display_name": "Image", "data_type": "Image", "source_type": "user", "required": true, "ui_control": "oj-image", "is_visible": true, "allowed_values": null, "validations": {"max_size": 5000000, "extensions": [".jpg", ".png", ".gif"]}, "contextual_id": "go001.lo001.in027", "input_value": {"filename": "product_photo.jpg", "filesize": 1234567, "filetype": "image/jpeg", "dimensions": {"width": 1200, "height": 800}, "url": "https://storage.example.com/images/product_photo.jpg", "thumbnail_url": "https://storage.example.com/images/thumbnails/product_photo.jpg"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Upload an image", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in028", "input_stack_id": 1001, "attribute_id": "at028", "entity_id": "e001", "display_name": "Image Avatar", "data_type": "Image", "source_type": "user", "required": true, "ui_control": "oj-image-avatar", "is_visible": true, "allowed_values": null, "validations": {"max_size": 2000000, "extensions": [".jpg", ".png"]}, "contextual_id": "go001.lo001.in028", "input_value": {"filename": "avatar.png", "filesize": 102400, "filetype": "image/png", "dimensions": {"width": 256, "height": 256}, "url": "https://storage.example.com/avatars/user123.png"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Upload an avatar image", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in029", "input_stack_id": 1001, "attribute_id": "at029", "entity_id": "e001", "display_name": "Input Slider", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-input-slider", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100, "step": 5}, "contextual_id": "go001.lo001.in029", "input_value": 75, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select value with slider", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in030", "input_stack_id": 1001, "attribute_id": "at030", "entity_id": "e001", "display_name": "Integer", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-input-number", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100}, "contextual_id": "go001.lo001.in030", "input_value": 42, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter an integer", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in031", "input_stack_id": 1001, "attribute_id": "at031", "entity_id": "e001", "display_name": "Label", "data_type": "String", "source_type": "information", "required": false, "ui_control": "oj-label", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in031", "input_value": "This is a label", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display information", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in032", "input_stack_id": 1001, "attribute_id": "at032", "entity_id": "e001", "display_name": "Label with Image", "data_type": "String", "source_type": "information", "required": false, "ui_control": "oj-label-image", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in032", "input_value": {"text": "Label with image", "image_url": "https://example.com/icons/info.png"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display label with image", "is_informational": true, "has_dropdown_source": false, "image_url": "https://example.com/icons/info.png"}}, {"input_id": "in033", "input_stack_id": 1001, "attribute_id": "at033", "entity_id": "e001", "display_name": "List", "data_type": "Array", "source_type": "user", "required": true, "ui_control": "oj-list", "is_visible": true, "allowed_values": ["Item 1", "Item 2", "Item 3"], "validations": null, "contextual_id": "go001.lo001.in033", "input_value": ["Item 1", "Item 3"], "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select items from list", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Item 1", "label": "Item 1"}, {"value": "Item 2", "label": "Item 2"}, {"value": "Item 3", "label": "Item 3"}]}, {"input_id": "in034", "input_stack_id": 1001, "attribute_id": "at034", "entity_id": "e001", "display_name": "Location", "data_type": "Location", "source_type": "user", "required": true, "ui_control": "oj-location", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in034", "input_value": {"latitude": 17.385, "longitude": 78.4867, "address": "Hyderabad, Telangana, India"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter location coordinates", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in035", "input_stack_id": 1001, "attribute_id": "at035", "entity_id": "e001", "display_name": "Metric", "data_type": "Decimal", "source_type": "user", "required": true, "ui_control": "oj-metric", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "unit": "kg"}, "contextual_id": "go001.lo001.in035", "input_value": {"value": 75.5, "unit": "kg"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter a metric value", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in036", "input_stack_id": 1001, "attribute_id": "at036", "entity_id": "e001", "display_name": "Mobile No", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-input-mobile", "is_visible": true, "allowed_values": null, "validations": {"pattern": "^\\+?[1-9]\\d{9,14}$"}, "contextual_id": "go001.lo001.in036", "input_value": "+919876543210", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter mobile number", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in037", "input_stack_id": 1001, "attribute_id": "at037", "entity_id": "e001", "display_name": "Multiline", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-text-area", "is_visible": true, "allowed_values": null, "validations": {"max_length": 500}, "contextual_id": "go001.lo001.in037", "input_value": "This is a multiline text.\nIt contains multiple lines of content.\nEach on a new line.", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter multiline text", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in038", "input_stack_id": 1001, "attribute_id": "at038", "entity_id": "e001", "display_name": "Multiselect", "data_type": "Array", "source_type": "user", "required": true, "ui_control": "oj-combobox-many", "is_visible": true, "allowed_values": ["Option A", "Option B", "Option C", "Option D"], "validations": null, "contextual_id": "go001.lo001.in038", "input_value": ["Option A", "Option C", "Option D"], "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select multiple options", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Option A", "label": "Option A"}, {"value": "Option B", "label": "Option B"}, {"value": "Option C", "label": "Option C"}, {"value": "Option D", "label": "Option D"}]}, {"input_id": "in039", "input_stack_id": 1001, "attribute_id": "at039", "entity_id": "e001", "display_name": "Number", "data_type": "Number", "source_type": "user", "required": true, "ui_control": "oj-input-number", "is_visible": true, "allowed_values": null, "validations": {"min": -100, "max": 100}, "contextual_id": "go001.lo001.in039", "input_value": -25.75, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter a number", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in040", "input_stack_id": 1001, "attribute_id": "at040", "entity_id": "e001", "display_name": "Password", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-input-password", "is_visible": true, "allowed_values": null, "validations": {"min_length": 8, "pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"}, "contextual_id": "go001.lo001.in040", "input_value": "P@ssw0rd123", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter password", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in041", "input_stack_id": 1001, "attribute_id": "at041", "entity_id": "e001", "display_name": "Preview Doc", "data_type": "File", "source_type": "user", "required": true, "ui_control": "oj-preview-doc", "is_visible": true, "allowed_values": null, "validations": {"extensions": [".pdf", ".doc", ".docx"]}, "contextual_id": "go001.lo001.in041", "input_value": {"filename": "contract.pdf", "filesize": 3876543, "filetype": "application/pdf", "url": "https://storage.example.com/documents/contract.pdf", "preview_url": "https://storage.example.com/previews/contract.png", "page_count": 12}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Preview document", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in042", "input_stack_id": 1001, "attribute_id": "at042", "entity_id": "e001", "display_name": "Progressbar", "data_type": "Integer", "source_type": "system", "required": false, "ui_control": "oj-progressbar", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100}, "contextual_id": "go001.lo001.in042", "input_value": 75, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Show progress", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in043", "input_stack_id": 1001, "attribute_id": "at043", "entity_id": "e001", "display_name": "QR Code", "data_type": "String", "source_type": "system", "required": false, "ui_control": "oj-qr-code", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in043", "input_value": "https://example.com/product/12345", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display QR code", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in044", "input_stack_id": 1001, "attribute_id": "at044", "entity_id": "e001", "display_name": "QR Decoder", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-qr-decoder", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in044", "input_value": {"raw_data": "https://example.com/scan/abc123", "scan_timestamp": "2025-05-06T14:35:22", "format": "QR_CODE"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Decode QR code", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in045", "input_stack_id": 1001, "attribute_id": "at045", "entity_id": "e001", "display_name": "QR Scanner", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-qr-scanner", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in045", "input_value": {"scan_result": "INV-2025-12345", "scan_timestamp": "2025-05-06T15:10:45", "format": "QR_CODE"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Scan QR code", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in046", "input_stack_id": 1001, "attribute_id": "at046", "entity_id": "e001", "display_name": "Radio Button", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-radioset", "is_visible": true, "allowed_values": ["Option 1", "Option 2", "Option 3"], "validations": null, "contextual_id": "go001.lo001.in046", "input_value": "Option 3", "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select one option", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Option 1", "label": "Option 1"}, {"value": "Option 2", "label": "Option 2"}, {"value": "Option 3", "label": "Option 3"}]}, {"input_id": "in047", "input_stack_id": 1001, "attribute_id": "at047", "entity_id": "e001", "display_name": "Range Slider", "data_type": "Range", "source_type": "user", "required": true, "ui_control": "oj-range-slider", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100}, "contextual_id": "go001.lo001.in047", "input_value": [20, 80], "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select a range", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in048", "input_stack_id": 1001, "attribute_id": "at048", "entity_id": "e001", "display_name": "Rating", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-rating", "is_visible": true, "allowed_values": null, "validations": {"min": 1, "max": 5}, "contextual_id": "go001.lo001.in048", "input_value": 4, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Rate from 1-5", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in049", "input_stack_id": 1001, "attribute_id": "at049", "entity_id": "e001", "display_name": "Redact", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-redact", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in049", "input_value": {"original": "My credit card number is 4111-1111-1111-1111", "redacted": "My credit card number is XXXX-XXXX-XXXX-1111"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Redact sensitive information", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in050", "input_stack_id": 1001, "attribute_id": "at050", "entity_id": "e001", "display_name": "Redirection", "data_type": "URL", "source_type": "system", "required": false, "ui_control": "oj-redirection", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in050", "input_value": {"url": "https://example.com/redirect", "target": "_blank", "description": "Click to view details"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Redirect to URL", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in051", "input_stack_id": 1001, "attribute_id": "at051", "entity_id": "e001", "display_name": "Rich Text", "data_type": "HTML", "source_type": "user", "required": true, "ui_control": "oj-rich-text", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in051", "input_value": "<h3>Project Summary</h3><p>This is a <strong>rich text</strong> editor content with <em>formatting</em>.</p><ul><li>Point 1</li><li>Point 2</li></ul>", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter formatted text", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in052", "input_stack_id": 1001, "attribute_id": "at052", "entity_id": "e001", "display_name": "Scheduler", "data_type": "Schedule", "source_type": "user", "required": true, "ui_control": "oj-scheduler", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in052", "input_value": {"events": [{"id": "evt1", "title": "Team Meeting", "start": "2025-05-10T10:00:00", "end": "2025-05-10T11:00:00", "location": "Conference Room A"}, {"id": "evt2", "title": "Client Call", "start": "2025-05-12T14:00:00", "end": "2025-05-12T15:00:00", "location": "Virtual"}]}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Schedule events", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in053", "input_stack_id": 1001, "attribute_id": "at053", "entity_id": "e001", "display_name": "Signature", "data_type": "Image", "source_type": "user", "required": true, "ui_control": "oj-signature", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in053", "input_value": {"signature_id": "sig_45678", "timestamp": "2025-05-04T16:20:00", "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAA...", "signer_name": "<PERSON>"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Draw signature", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in054", "input_stack_id": 1001, "attribute_id": "at054", "entity_id": "e001", "display_name": "Step Slider", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-step-slider", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100, "step": 25}, "contextual_id": "go001.lo001.in054", "input_value": 50, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select value with step slider", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in055", "input_stack_id": 1001, "attribute_id": "at055", "entity_id": "e001", "display_name": "Streaming Video", "data_type": "Video", "source_type": "system", "required": false, "ui_control": "oj-streaming-video", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in055", "input_value": {"url": "https://example.com/videos/training.mp4", "type": "video/mp4", "title": "Training Video", "thumbnail": "https://example.com/thumbnails/training.jpg", "duration": 1280, "autoplay": false}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Stream video", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in056", "input_stack_id": 1001, "attribute_id": "at056", "entity_id": "e001", "display_name": "Text", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-input-text", "is_visible": true, "allowed_values": null, "validations": {"max_length": 100}, "contextual_id": "go001.lo001.in056", "input_value": "Sample text input", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter text", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in057", "input_stack_id": 1001, "attribute_id": "at057", "entity_id": "e001", "display_name": "Text Avatar", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-text-avatar", "is_visible": true, "allowed_values": null, "validations": {"max_length": 2}, "contextual_id": "go001.lo001.in057", "input_value": {"text": "JS", "background_color": "#4285F4", "text_color": "#FFFFFF"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Enter initials for avatar", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in058", "input_stack_id": 1001, "attribute_id": "at058", "entity_id": "e001", "display_name": "Time", "data_type": "Time", "source_type": "user", "required": true, "ui_control": "oj-input-time", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in058", "input_value": "15:45:00", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select time", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in059", "input_stack_id": 1001, "attribute_id": "at059", "entity_id": "e001", "display_name": "Time Only", "data_type": "Time", "source_type": "user", "required": true, "ui_control": "oj-time-only", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in059", "input_value": "09:15:00", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select time only", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in060", "input_stack_id": 1001, "attribute_id": "at060", "entity_id": "e001", "display_name": "Timer", "data_type": "Duration", "source_type": "system", "required": false, "ui_control": "oj-timer", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in060", "input_value": {"duration": "PT30M", "start_time": "2025-05-06T14:00:00", "end_time": "2025-05-06T14:30:00", "is_running": true}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display timer", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in061", "input_stack_id": 1001, "attribute_id": "at061", "entity_id": "e001", "display_name": "Typeahead", "data_type": "String", "source_type": "user", "required": true, "ui_control": "oj-<PERSON><PERSON><PERSON>", "is_visible": true, "allowed_values": ["Apple", "Banana", "Cherry", "<PERSON><PERSON>", "<PERSON><PERSON>"], "validations": null, "contextual_id": "go001.lo001.in061", "input_value": "Apple", "has_dropdown_source": true, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Type with suggestions", "is_informational": false, "has_dropdown_source": true}, "dropdown_options": [{"value": "Apple", "label": "Apple"}, {"value": "Banana", "label": "Banana"}, {"value": "Cherry", "label": "Cherry"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}]}, {"input_id": "in062", "input_stack_id": 1001, "attribute_id": "at062", "entity_id": "e001", "display_name": "Vertical Slider", "data_type": "Integer", "source_type": "user", "required": true, "ui_control": "oj-vertical-slider", "is_visible": true, "allowed_values": null, "validations": {"min": 0, "max": 100}, "contextual_id": "go001.lo001.in062", "input_value": 65, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select value with vertical slider", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in063", "input_stack_id": 1001, "attribute_id": "at063", "entity_id": "e001", "display_name": "Video", "data_type": "Video", "source_type": "user", "required": true, "ui_control": "oj-video", "is_visible": true, "allowed_values": null, "validations": {"max_size": 50000000, "extensions": [".mp4", ".mov", ".avi"]}, "contextual_id": "go001.lo001.in063", "input_value": {"filename": "product_demo.mp4", "filesize": 25678901, "filetype": "video/mp4", "dimensions": {"width": 1920, "height": 1080}, "duration": 345, "url": "https://storage.example.com/videos/product_demo.mp4", "thumbnail_url": "https://storage.example.com/thumbnails/product_demo.jpg"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Upload video file", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in064", "input_stack_id": 1001, "attribute_id": "at064", "entity_id": "e001", "display_name": "Video Recording", "data_type": "Video", "source_type": "user", "required": true, "ui_control": "oj-video-recording", "is_visible": true, "allowed_values": null, "validations": {"max_duration": 300}, "contextual_id": "go001.lo001.in064", "input_value": {"recording_id": "rec_video_87654", "duration": 120, "filetype": "video/mp4", "dimensions": {"width": 1280, "height": 720}, "timestamp": "2025-05-06T13:45:22", "url": "https://storage.example.com/recordings/rec_video_87654.mp4"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Record video", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in065", "input_stack_id": 1001, "attribute_id": "at065", "entity_id": "e001", "display_name": "Year", "data_type": "Year", "source_type": "user", "required": true, "ui_control": "oj-year", "is_visible": true, "allowed_values": null, "validations": {"min": 2000, "max": 2030}, "contextual_id": "go001.lo001.in065", "input_value": 2025, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select year", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in066", "input_stack_id": 1001, "attribute_id": "at066", "entity_id": "e001", "display_name": "Year Month", "data_type": "YearMonth", "source_type": "user", "required": true, "ui_control": "oj-year-month", "is_visible": true, "allowed_values": null, "validations": {"min": "2020-01", "max": "2030-12"}, "contextual_id": "go001.lo001.in066", "input_value": "2025-05", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Select year and month", "is_informational": false, "has_dropdown_source": false}}, {"input_id": "in067", "input_stack_id": 1001, "attribute_id": "at067", "entity_id": "e001", "display_name": "CU Clock", "data_type": "Time", "source_type": "system", "required": false, "ui_control": "oj-cu-clock", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in067", "input_value": {"time": "14:30:00", "display_type": "analog", "timezone": "Asia/Kolkata"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display analog clock", "is_informational": true, "has_dropdown_source": false}}, {"input_id": "in068", "input_stack_id": 1001, "attribute_id": "at068", "entity_id": "e001", "display_name": "CTA / Button Tag", "data_type": "Action", "source_type": "action", "required": false, "ui_control": "oj-button", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in068", "input_value": {"label": "Submit", "action_type": "submit", "style": "primary", "icon": "check"}, "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Action button", "is_informational": false, "has_dropdown_source": false, "action_type": "submit"}}], "system_inputs": [{"input_id": "in069", "input_stack_id": 1001, "attribute_id": "at069", "entity_id": "e001", "display_name": "System ID", "data_type": "String", "source_type": "system", "required": true, "ui_control": "oj-input-text", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in069", "input_value": "SYS12345", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "System generated ID", "is_informational": false, "has_dropdown_source": false}}], "info_inputs": [{"input_id": "in070", "input_stack_id": 1001, "attribute_id": "at070", "entity_id": "e001", "display_name": "Information Message", "data_type": "String", "source_type": "information", "required": false, "ui_control": "oj-text", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in070", "input_value": "This is an informational message", "has_dropdown_source": false, "dependencies": null, "dependency_type": null, "metadata": {"usage": "Display information", "is_informational": true, "has_dropdown_source": false}}], "dependent_inputs": [{"input_id": "in071", "input_stack_id": 1001, "attribute_id": "at071", "entity_id": "e001", "display_name": "Sub Category", "data_type": "String", "source_type": "system_dependent", "required": true, "ui_control": "oj-combobox-one", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in071", "input_value": "Sub Option 2", "has_dropdown_source": true, "dependencies": ["in019"], "dependency_type": "dropdown", "metadata": {"usage": "Select sub-category", "is_informational": false, "has_dropdown_source": true}, "needs_parent_value": true, "parent_ids": ["in019"], "dropdown_options": [{"value": "Sub Option 1", "label": "Sub Option 1", "parent_value": "Option 1"}, {"value": "Sub Option 2", "label": "Sub Option 2", "parent_value": "Option 2"}, {"value": "Sub Option 3", "label": "Sub Option 3", "parent_value": "Option 3"}]}, {"input_id": "in072", "input_stack_id": 1001, "attribute_id": "at072", "entity_id": "e001", "display_name": "Total Amount", "data_type": "Decimal", "source_type": "system_dependent", "required": true, "ui_control": "oj-input-number", "is_visible": true, "allowed_values": null, "validations": null, "contextual_id": "go001.lo001.in072", "input_value": 16.75, "has_dropdown_source": false, "dependencies": ["in017", "in039"], "dependency_type": "calculation", "metadata": {"usage": "Calculate total amount", "is_informational": false, "has_dropdown_source": false, "calculation": "in017 + Math.abs(in039)"}, "needs_parent_value": true, "parent_ids": ["in017", "in039"]}], "dependencies": {"in071": ["in019"], "in072": ["in017", "in039"]}}