name: ui_controls_library
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.2
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  image_picker: ^1.0.4
  intl: ^0.19.0
  camera: ^0.11.1
  permission_handler: ^11.3.0
  file_picker: ^10.1.2
  path: ^1.8.3
  path_provider: ^2.1.2
  flutter_html: ^3.0.0-beta.2
  url_launcher: ^6.2.4
  geolocator: ^11.0.0
  geocoding: ^2.2.0
  google_maps_flutter: ^2.6.0
  mobile_scanner: ^6.0.10
  record: ^6.0.0
  audioplayers: ^6.0.0
  video_player: ^2.8.2
  chewie: ^1.7.5
  better_player: ^0.0.84
  shared_preferences: ^2.2.2
  flutter_svg: ^2.0.9


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

dependency_overrides:
  better_player: ^0.0.84
  collection: ^1.18.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/images/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
