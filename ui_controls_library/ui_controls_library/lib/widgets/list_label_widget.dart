import 'dart:convert';
import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A customizable widget for displaying a list of labeled items.
///
/// This widget provides a rich set of customization options for displaying
/// lists with labels, including various styling options, layout configurations,
/// and interactive features.
class ListLabelWidget extends StatefulWidget {
  /// The list of items to display.
  final List<String> items;

  /// The title of the list.
  final String? title;

  /// The style of the title text.
  final TextStyle? titleStyle;

  /// The style of the item text.
  final TextStyle? itemStyle;

  /// The font size of the title.
  final double? titleFontSize;

  /// The font size of the items.
  final double? itemFontSize;

  /// The font weight of the title.
  final FontWeight? titleFontWeight;

  /// The font weight of the items.
  final FontWeight? itemFontWeight;

  /// The font style of the title (normal or italic).
  final FontStyle? titleFontStyle;

  /// The font style of the items (normal or italic).
  final FontStyle? itemFontStyle;

  /// The color of the title text.
  final Color? titleColor;

  /// The color of the item text.
  final Color? itemColor;

  /// The background color of the widget.
  final Color? backgroundColor;

  /// The background color of the title.
  final Color? titleBackgroundColor;

  /// The background color of the items.
  final Color? itemBackgroundColor;

  /// The alignment of the title text.
  final TextAlign titleAlign;

  /// The alignment of the item text.
  final TextAlign itemAlign;

  /// The maximum number of lines for the title.
  final int? titleMaxLines;

  /// The maximum number of lines for each item.
  final int? itemMaxLines;

  /// Whether to overflow the title text with an ellipsis.
  final bool titleOverflow;

  /// Whether to overflow the item text with an ellipsis.
  final bool itemOverflow;

  /// The width of the widget.
  final double? width;

  /// The height of the widget.
  final double? height;

  /// The padding around the widget.
  final EdgeInsetsGeometry padding;

  /// The margin around the widget.
  final EdgeInsetsGeometry margin;

  /// The padding around the title.
  final EdgeInsetsGeometry titlePadding;

  /// The padding around each item.
  final EdgeInsetsGeometry itemPadding;

  /// The spacing between the title and the items.
  final double titleItemSpacing;

  /// The spacing between items.
  final double itemSpacing;

  /// The border radius of the widget.
  final double borderRadius;

  /// The border radius of the title.
  final double titleBorderRadius;

  /// The border radius of the items.
  final double itemBorderRadius;

  /// The color of the widget's border.
  final Color? borderColor;

  /// The color of the title's border.
  final Color? titleBorderColor;

  /// The color of the items' border.
  final Color? itemBorderColor;

  /// The width of the widget's border.
  final double borderWidth;

  /// The width of the title's border.
  final double titleBorderWidth;

  /// The width of the items' border.
  final double itemBorderWidth;

  /// Whether to show a shadow under the widget.
  final bool shadow;

  /// Whether to show a shadow under the title.
  final bool titleShadow;

  /// Whether to show a shadow under the items.
  final bool itemShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The elevation of the title's shadow.
  final double titleElevation;

  /// The elevation of the items' shadow.
  final double itemElevation;

  /// The color of the shadow.
  final Color? shadowColor;

  /// The color of the title's shadow.
  final Color? titleShadowColor;

  /// The color of the items' shadow.
  final Color? itemShadowColor;

  /// The type of bullet to use for the items.
  final BulletType bulletType;

  /// The color of the bullets.
  final Color? bulletColor;

  /// The size of the bullets.
  final double bulletSize;

  /// The icon to use as a bullet (if bulletType is icon).
  final IconData? bulletIcon;

  /// The spacing between the bullet and the item text.
  final double bulletSpacing;

  /// Whether to make the title bold.
  final bool titleBold;

  /// Whether to make the items bold.
  final bool itemBold;

  /// Whether to make the title italic.
  final bool titleItalic;

  /// Whether to make the items italic.
  final bool itemItalic;

  /// Whether to make the title uppercase.
  final bool titleUppercase;

  /// Whether to make the items uppercase.
  final bool itemUppercase;

  /// Whether to make the title lowercase.
  final bool titleLowercase;

  /// Whether to make the items lowercase.
  final bool itemLowercase;

  /// Whether to capitalize the first letter of each word in the title.
  final bool titleCapitalize;

  /// Whether to capitalize the first letter of each word in the items.
  final bool itemCapitalize;

  /// The tooltip text to show when hovering over the widget.
  final String? tooltip;

  /// The callback to execute when an item is tapped.
  final void Function(int index)? onItemTap;

  /// The callback to execute when an item is long-pressed.
  final void Function(int index)? onItemLongPress;

  /// The callback to execute when the title is tapped.
  final VoidCallback? onTitleTap;

  /// The callback to execute when the title is long-pressed.
  final VoidCallback? onTitleLongPress;

  /// Whether to show dividers between items.
  final bool showDividers;

  /// The color of the dividers.
  final Color? dividerColor;

  /// The thickness of the dividers.
  final double dividerThickness;

  /// The indent of the dividers.
  final double dividerIndent;

  /// The end indent of the dividers.
  final double dividerEndIndent;

  /// Whether to show a divider after the title.
  final bool showTitleDivider;

  /// The color of the title divider.
  final Color? titleDividerColor;

  /// The thickness of the title divider.
  final double titleDividerThickness;

  /// The indent of the title divider.
  final double titleDividerIndent;

  /// The end indent of the title divider.
  final double titleDividerEndIndent;

  /// Whether to show numbers instead of bullets.
  final bool numbered;

  /// The style of the numbers (if numbered is true).
  final NumberStyle numberStyle;

  /// The color of the numbers (if numbered is true).
  final Color? numberColor;

  /// The format of the numbers (if numbered is true).
  final String numberFormat;

  /// Whether to make the list items selectable.
  final bool selectable;

  /// The initially selected item index.
  final int? initialSelectedIndex;

  /// The color of the selected item.
  final Color? selectedItemColor;

  /// The background color of the selected item.
  final Color? selectedItemBackgroundColor;

  /// The callback to execute when the selection changes.
  final void Function(int? index)? onSelectionChanged;

  /// Whether to allow multiple selection.
  final bool multipleSelection;

  /// The initially selected item indices (if multipleSelection is true).
  final List<int>? initialSelectedIndices;

  /// The callback to execute when the multiple selection changes.
  final void Function(List<int> indices)? onMultipleSelectionChanged;

  /// Whether to show checkboxes for the items.
  final bool showCheckboxes;

  /// The color of the checkboxes.
  final Color? checkboxColor;

  /// The size of the checkboxes.
  final double checkboxSize;

  /// Whether to show a scrollbar.
  final bool showScrollbar;

  /// The thickness of the scrollbar.
  final double scrollbarThickness;

  /// The radius of the scrollbar.
  final double scrollbarRadius;

  /// Whether to always show the scrollbar.
  final bool alwaysShowScrollbar;

  /// The maximum height of the list.
  final double? maxHeight;

  /// Whether to shrink wrap the list.
  final bool shrinkWrap;

  /// The physics of the list.
  final ScrollPhysics? physics;

  /// The padding around the scrollbar.
  final EdgeInsetsGeometry scrollbarPadding;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // List-label-specific JSON configuration
  /// Whether to use JSON list-label configuration
  final bool useJsonListLabelConfig;

  /// List-label-specific JSON configuration
  final Map<String, dynamic>? listLabelConfig;

  /// Creates a list label widget.
  const ListLabelWidget({
    super.key,
    required this.items,
    this.title,
    this.titleStyle,
    this.itemStyle,
    this.titleFontSize,
    this.itemFontSize,
    this.titleFontWeight,
    this.itemFontWeight,
    this.titleFontStyle,
    this.itemFontStyle,
    this.titleColor,
    this.itemColor,
    this.backgroundColor,
    this.titleBackgroundColor,
    this.itemBackgroundColor,
    this.titleAlign = TextAlign.start,
    this.itemAlign = TextAlign.start,
    this.titleMaxLines,
    this.itemMaxLines,
    this.titleOverflow = false,
    this.itemOverflow = false,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = EdgeInsets.zero,
    this.titlePadding = const EdgeInsets.symmetric(vertical: 8.0),
    this.itemPadding = const EdgeInsets.symmetric(vertical: 4.0),
    this.titleItemSpacing = 8.0,
    this.itemSpacing = 4.0,
    this.borderRadius = 0.0,
    this.titleBorderRadius = 0.0,
    this.itemBorderRadius = 0.0,
    this.borderColor,
    this.titleBorderColor,
    this.itemBorderColor,
    this.borderWidth = 0.0,
    this.titleBorderWidth = 0.0,
    this.itemBorderWidth = 0.0,
    this.shadow = false,
    this.titleShadow = false,
    this.itemShadow = false,
    this.elevation = 2.0,
    this.titleElevation = 1.0,
    this.itemElevation = 1.0,
    this.shadowColor,
    this.titleShadowColor,
    this.itemShadowColor,
    this.bulletType = BulletType.dot,
    this.bulletColor,
    this.bulletSize = 6.0,
    this.bulletIcon,
    this.bulletSpacing = 8.0,
    this.titleBold = false,
    this.itemBold = false,
    this.titleItalic = false,
    this.itemItalic = false,
    this.titleUppercase = false,
    this.itemUppercase = false,
    this.titleLowercase = false,
    this.itemLowercase = false,
    this.titleCapitalize = false,
    this.itemCapitalize = false,
    this.tooltip,
    this.onItemTap,
    this.onItemLongPress,
    this.onTitleTap,
    this.onTitleLongPress,
    this.showDividers = false,
    this.dividerColor,
    this.dividerThickness = 1.0,
    this.dividerIndent = 0.0,
    this.dividerEndIndent = 0.0,
    this.showTitleDivider = false,
    this.titleDividerColor,
    this.titleDividerThickness = 1.0,
    this.titleDividerIndent = 0.0,
    this.titleDividerEndIndent = 0.0,
    this.numbered = false,
    this.numberStyle = NumberStyle.decimal,
    this.numberColor,
    this.numberFormat = '%d.',
    this.selectable = false,
    this.initialSelectedIndex,
    this.selectedItemColor,
    this.selectedItemBackgroundColor,
    this.onSelectionChanged,
    this.multipleSelection = false,
    this.initialSelectedIndices,
    this.onMultipleSelectionChanged,
    this.showCheckboxes = false,
    this.checkboxColor,
    this.checkboxSize = 18.0,
    this.showScrollbar = false,
    this.scrollbarThickness = 6.0,
    this.scrollbarRadius = 3.0,
    this.alwaysShowScrollbar = false,
    this.maxHeight,
    this.shrinkWrap = true,
    this.physics,
    this.scrollbarPadding = EdgeInsets.zero,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // List-label-specific JSON configuration
    this.useJsonListLabelConfig = false,
    this.listLabelConfig,
  });

  /// Creates a ListLabelWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ListLabelWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "title": "My List",
  ///   "items": ["Item 1", "Item 2", "Item 3"],
  ///   "titleColor": "#FF0000",
  ///   "itemColor": "#0000FF",
  ///   "bulletType": "dot"
  /// }
  /// ```
  factory ListLabelWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'amber': return Colors.amber;
          case 'cyan': return Colors.cyan;
          case 'indigo': return Colors.indigo;
          case 'lime': return Colors.lime;
          case 'teal': return Colors.teal;
          default: return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom = (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') || insetsValue.containsKey('vertical')) {
          final double horizontal = (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical = (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds = (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms': return Duration(milliseconds: value);
            case 's': return Duration(seconds: value);
            case 'm': return Duration(minutes: value);
            default: return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear': return Curves.linear;
          case 'decelerate': return Curves.decelerate;
          case 'ease': return Curves.ease;
          case 'easein':
          case 'ease_in': return Curves.easeIn;
          case 'easeout':
          case 'ease_out': return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out': return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in': return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out': return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out': return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in': return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out': return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out': return Curves.bounceInOut;
          default: return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize = styleValue['fontSize'] != null ? (styleValue['fontSize'] as num).toDouble() : null;
        final fontWeight = styleValue['fontWeight'] != null ?
          (styleValue['fontWeight'] == 'bold' ? FontWeight.bold :
           styleValue['fontWeight'] == 'normal' ? FontWeight.normal :
           FontWeight.normal) : null;
        final fontStyle = styleValue['fontStyle'] != null ?
          (styleValue['fontStyle'] == 'italic' ? FontStyle.italic :
           FontStyle.normal) : null;
        final letterSpacing = styleValue['letterSpacing'] != null ? (styleValue['letterSpacing'] as num).toDouble() : null;
        final wordSpacing = styleValue['wordSpacing'] != null ? (styleValue['wordSpacing'] as num).toDouble() : null;
        final height = styleValue['height'] != null ? (styleValue['height'] as num).toDouble() : null;
        final decoration = styleValue['decoration'] != null ?
          (styleValue['decoration'] == 'underline' ? TextDecoration.underline :
           styleValue['decoration'] == 'lineThrough' ? TextDecoration.lineThrough :
           styleValue['decoration'] == 'overline' ? TextDecoration.overline :
           null) : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse text align
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'left': return TextAlign.left;
          case 'right': return TextAlign.right;
          case 'center': return TextAlign.center;
          case 'justify': return TextAlign.justify;
          case 'start': return TextAlign.start;
          case 'end': return TextAlign.end;
          default: return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight? parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return null;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'w100': return FontWeight.w100;
          case 'w200': return FontWeight.w200;
          case 'w300': return FontWeight.w300;
          case 'w400': return FontWeight.w400;
          case 'w500': return FontWeight.w500;
          case 'w600': return FontWeight.w600;
          case 'w700': return FontWeight.w700;
          case 'w800': return FontWeight.w800;
          case 'w900': return FontWeight.w900;
          default: return null;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100: return FontWeight.w100;
          case 200: return FontWeight.w200;
          case 300: return FontWeight.w300;
          case 400: return FontWeight.w400;
          case 500: return FontWeight.w500;
          case 600: return FontWeight.w600;
          case 700: return FontWeight.w700;
          case 800: return FontWeight.w800;
          case 900: return FontWeight.w900;
          default: return null;
        }
      }

      return null;
    }

    // Parse font style
    FontStyle? parseFontStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is String) {
        switch (styleValue.toLowerCase()) {
          case 'italic': return FontStyle.italic;
          case 'normal': return FontStyle.normal;
          default: return null;
        }
      }

      return null;
    }

    // Parse bullet type
    BulletType parseBulletType(dynamic bulletTypeValue) {
      if (bulletTypeValue == null) return BulletType.dot;

      if (bulletTypeValue is String) {
        switch (bulletTypeValue.toLowerCase()) {
          case 'dot': return BulletType.dot;
          case 'dash': return BulletType.dash;
          case 'square': return BulletType.square;
          case 'diamond': return BulletType.diamond;
          case 'circle': return BulletType.circle;
          case 'arrow': return BulletType.arrow;
          case 'icon': return BulletType.icon;
          case 'none': return BulletType.none;
          default: return BulletType.dot;
        }
      }

      return BulletType.dot;
    }

    // Parse number style
    NumberStyle parseNumberStyle(dynamic numberStyleValue) {
      if (numberStyleValue == null) return NumberStyle.decimal;

      if (numberStyleValue is String) {
        switch (numberStyleValue.toLowerCase()) {
          case 'decimal': return NumberStyle.decimal;
          case 'loweralpha':
          case 'lower_alpha': return NumberStyle.lowerAlpha;
          case 'upperalpha':
          case 'upper_alpha': return NumberStyle.upperAlpha;
          case 'lowerroman':
          case 'lower_roman': return NumberStyle.lowerRoman;
          case 'upperroman':
          case 'upper_roman': return NumberStyle.upperRoman;
          default: return NumberStyle.decimal;
        }
      }

      return NumberStyle.decimal;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        // This is a simplified version. In a real app, you would need a more comprehensive mapping
        switch (iconValue.toLowerCase()) {
          case 'add': return Icons.add;
          case 'remove': return Icons.remove;
          case 'edit': return Icons.edit;
          case 'delete': return Icons.delete;
          case 'search': return Icons.search;
          case 'home': return Icons.home;
          case 'settings': return Icons.settings;
          case 'person': return Icons.person;
          case 'info': return Icons.info;
          case 'warning': return Icons.warning;
          case 'error': return Icons.error;
          case 'check': return Icons.check;
          case 'close': return Icons.close;
          case 'menu': return Icons.menu;
          case 'more': return Icons.more_vert;
          case 'arrow_back': return Icons.arrow_back;
          case 'arrow_forward': return Icons.arrow_forward;
          case 'arrow_up': return Icons.arrow_upward;
          case 'arrow_down': return Icons.arrow_downward;
          case 'star': return Icons.star;
          case 'favorite': return Icons.favorite;
          case 'thumb_up': return Icons.thumb_up;
          case 'thumb_down': return Icons.thumb_down;
          case 'image': return Icons.image;
          case 'broken_image': return Icons.broken_image;
          case 'photo': return Icons.photo;
          case 'camera': return Icons.camera_alt;
          default: return null;
        }
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks = jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onItemTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onItemTap'] = json['onItemTap'];
      useJsonCallbacks = true;
    }

    if (json['onItemLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onItemLongPress'] = json['onItemLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onTitleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTitleTap'] = json['onTitleTap'];
      useJsonCallbacks = true;
    }

    if (json['onTitleLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTitleLongPress'] = json['onTitleLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onSelectionChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSelectionChanged'] = json['onSelectionChanged'];
      useJsonCallbacks = true;
    }

    if (json['onMultipleSelectionChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onMultipleSelectionChanged'] = json['onMultipleSelectionChanged'];
      useJsonCallbacks = true;
    }

    // Parse List-label-specific configuration
    Map<String, dynamic>? listLabelConfig;
    bool useJsonListLabelConfig = json['useJsonListLabelConfig'] as bool? ?? false;

    if (json['listLabelConfig'] != null) {
      if (json['listLabelConfig'] is Map) {
        listLabelConfig = Map<String, dynamic>.from(json['listLabelConfig'] as Map);
        useJsonListLabelConfig = true;
      } else if (json['listLabelConfig'] is String) {
        try {
          listLabelConfig = jsonDecode(json['listLabelConfig'] as String) as Map<String, dynamic>;
          useJsonListLabelConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return ListLabelWidget(
      // Basic properties
      items: (json['items'] as List<dynamic>?)?.map((item) => item.toString()).toList() ?? [],
      title: json['title'] as String?,
      titleStyle: parseTextStyle(json['titleStyle']),
      itemStyle: parseTextStyle(json['itemStyle']),
      titleFontSize: json['titleFontSize'] != null ? (json['titleFontSize'] as num).toDouble() : null,
      itemFontSize: json['itemFontSize'] != null ? (json['itemFontSize'] as num).toDouble() : null,
      titleFontWeight: parseFontWeight(json['titleFontWeight']),
      itemFontWeight: parseFontWeight(json['itemFontWeight']),
      titleFontStyle: parseFontStyle(json['titleFontStyle']),
      itemFontStyle: parseFontStyle(json['itemFontStyle']),
      titleColor: parseColor(json['titleColor']),
      itemColor: parseColor(json['itemColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      titleBackgroundColor: parseColor(json['titleBackgroundColor']),
      itemBackgroundColor: parseColor(json['itemBackgroundColor']),
      titleAlign: parseTextAlign(json['titleAlign']),
      itemAlign: parseTextAlign(json['itemAlign']),
      titleMaxLines: json['titleMaxLines'] as int?,
      itemMaxLines: json['itemMaxLines'] as int?,
      titleOverflow: json['titleOverflow'] as bool? ?? false,
      itemOverflow: json['itemOverflow'] as bool? ?? false,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      titlePadding: parseEdgeInsets(json['titlePadding']),
      itemPadding: parseEdgeInsets(json['itemPadding']),
      titleItemSpacing: json['titleItemSpacing'] != null ? (json['titleItemSpacing'] as num).toDouble() : 8.0,
      itemSpacing: json['itemSpacing'] != null ? (json['itemSpacing'] as num).toDouble() : 4.0,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 0.0,
      titleBorderRadius: json['titleBorderRadius'] != null ? (json['titleBorderRadius'] as num).toDouble() : 0.0,
      itemBorderRadius: json['itemBorderRadius'] != null ? (json['itemBorderRadius'] as num).toDouble() : 0.0,
      borderColor: parseColor(json['borderColor']),
      titleBorderColor: parseColor(json['titleBorderColor']),
      itemBorderColor: parseColor(json['itemBorderColor']),
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 0.0,
      titleBorderWidth: json['titleBorderWidth'] != null ? (json['titleBorderWidth'] as num).toDouble() : 0.0,
      itemBorderWidth: json['itemBorderWidth'] != null ? (json['itemBorderWidth'] as num).toDouble() : 0.0,
      shadow: json['shadow'] as bool? ?? false,
      titleShadow: json['titleShadow'] as bool? ?? false,
      itemShadow: json['itemShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 2.0,
      titleElevation: json['titleElevation'] != null ? (json['titleElevation'] as num).toDouble() : 1.0,
      itemElevation: json['itemElevation'] != null ? (json['itemElevation'] as num).toDouble() : 1.0,
      shadowColor: parseColor(json['shadowColor']),
      titleShadowColor: parseColor(json['titleShadowColor']),
      itemShadowColor: parseColor(json['itemShadowColor']),
      bulletType: parseBulletType(json['bulletType']),
      bulletColor: parseColor(json['bulletColor']),
      bulletSize: json['bulletSize'] != null ? (json['bulletSize'] as num).toDouble() : 6.0,
      bulletIcon: parseIconData(json['bulletIcon']),
      bulletSpacing: json['bulletSpacing'] != null ? (json['bulletSpacing'] as num).toDouble() : 8.0,
      titleBold: json['titleBold'] as bool? ?? false,
      itemBold: json['itemBold'] as bool? ?? false,
      titleItalic: json['titleItalic'] as bool? ?? false,
      itemItalic: json['itemItalic'] as bool? ?? false,
      titleUppercase: json['titleUppercase'] as bool? ?? false,
      itemUppercase: json['itemUppercase'] as bool? ?? false,
      titleLowercase: json['titleLowercase'] as bool? ?? false,
      itemLowercase: json['itemLowercase'] as bool? ?? false,
      titleCapitalize: json['titleCapitalize'] as bool? ?? false,
      itemCapitalize: json['itemCapitalize'] as bool? ?? false,
      tooltip: json['tooltip'] as String?,
      showDividers: json['showDividers'] as bool? ?? false,
      dividerColor: parseColor(json['dividerColor']),
      dividerThickness: json['dividerThickness'] != null ? (json['dividerThickness'] as num).toDouble() : 1.0,
      dividerIndent: json['dividerIndent'] != null ? (json['dividerIndent'] as num).toDouble() : 0.0,
      dividerEndIndent: json['dividerEndIndent'] != null ? (json['dividerEndIndent'] as num).toDouble() : 0.0,
      showTitleDivider: json['showTitleDivider'] as bool? ?? false,
      titleDividerColor: parseColor(json['titleDividerColor']),
      titleDividerThickness: json['titleDividerThickness'] != null ? (json['titleDividerThickness'] as num).toDouble() : 1.0,
      titleDividerIndent: json['titleDividerIndent'] != null ? (json['titleDividerIndent'] as num).toDouble() : 0.0,
      titleDividerEndIndent: json['titleDividerEndIndent'] != null ? (json['titleDividerEndIndent'] as num).toDouble() : 0.0,
      numbered: json['numbered'] as bool? ?? false,
      numberStyle: parseNumberStyle(json['numberStyle']),
      numberColor: parseColor(json['numberColor']),
      numberFormat: json['numberFormat'] as String? ?? '%d.',
      selectable: json['selectable'] as bool? ?? false,
      initialSelectedIndex: json['initialSelectedIndex'] as int?,
      selectedItemColor: parseColor(json['selectedItemColor']),
      selectedItemBackgroundColor: parseColor(json['selectedItemBackgroundColor']),
      multipleSelection: json['multipleSelection'] as bool? ?? false,
      initialSelectedIndices: (json['initialSelectedIndices'] as List<dynamic>?)?.map((i) => i as int).toList(),
      showCheckboxes: json['showCheckboxes'] as bool? ?? false,
      checkboxColor: parseColor(json['checkboxColor']),
      checkboxSize: json['checkboxSize'] != null ? (json['checkboxSize'] as num).toDouble() : 18.0,
      showScrollbar: json['showScrollbar'] as bool? ?? false,
      scrollbarThickness: json['scrollbarThickness'] != null ? (json['scrollbarThickness'] as num).toDouble() : 6.0,
      scrollbarRadius: json['scrollbarRadius'] != null ? (json['scrollbarRadius'] as num).toDouble() : 3.0,
      alwaysShowScrollbar: json['alwaysShowScrollbar'] as bool? ?? false,
      maxHeight: json['maxHeight'] != null ? (json['maxHeight'] as num).toDouble() : null,
      shrinkWrap: json['shrinkWrap'] as bool? ?? true,
      physics: json['physics'] == 'never' ? const NeverScrollableScrollPhysics() :
               json['physics'] == 'always' ? const AlwaysScrollableScrollPhysics() :
               json['physics'] == 'clamping' ? const ClampingScrollPhysics() :
               json['physics'] == 'bouncing' ? const BouncingScrollPhysics() : null,
      scrollbarPadding: parseEdgeInsets(json['scrollbarPadding']),

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly

      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: json['callbackState'] != null ? Map<String, dynamic>.from(json['callbackState'] as Map) : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // List-label-specific JSON configuration
      useJsonListLabelConfig: useJsonListLabelConfig,
      listLabelConfig: listLabelConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'items': items,
      'title': title,
      'titleAlign': titleAlign.toString().split('.').last,
      'itemAlign': itemAlign.toString().split('.').last,
      'titleMaxLines': titleMaxLines,
      'itemMaxLines': itemMaxLines,
      'titleOverflow': titleOverflow,
      'itemOverflow': itemOverflow,
      'width': width,
      'height': height,
      'titleItemSpacing': titleItemSpacing,
      'itemSpacing': itemSpacing,
      'borderRadius': borderRadius,
      'titleBorderRadius': titleBorderRadius,
      'itemBorderRadius': itemBorderRadius,
      'borderWidth': borderWidth,
      'titleBorderWidth': titleBorderWidth,
      'itemBorderWidth': itemBorderWidth,
      'shadow': shadow,
      'titleShadow': titleShadow,
      'itemShadow': itemShadow,
      'elevation': elevation,
      'titleElevation': titleElevation,
      'itemElevation': itemElevation,
      'bulletType': bulletType.toString().split('.').last,
      'bulletSize': bulletSize,
      'bulletSpacing': bulletSpacing,
      'titleBold': titleBold,
      'itemBold': itemBold,
      'titleItalic': titleItalic,
      'itemItalic': itemItalic,
      'titleUppercase': titleUppercase,
      'itemUppercase': itemUppercase,
      'titleLowercase': titleLowercase,
      'itemLowercase': itemLowercase,
      'titleCapitalize': titleCapitalize,
      'itemCapitalize': itemCapitalize,
      'tooltip': tooltip,
      'showDividers': showDividers,
      'dividerThickness': dividerThickness,
      'dividerIndent': dividerIndent,
      'dividerEndIndent': dividerEndIndent,
      'showTitleDivider': showTitleDivider,
      'titleDividerThickness': titleDividerThickness,
      'titleDividerIndent': titleDividerIndent,
      'titleDividerEndIndent': titleDividerEndIndent,
      'numbered': numbered,
      'numberStyle': numberStyle.toString().split('.').last,
      'numberFormat': numberFormat,
      'selectable': selectable,
      'initialSelectedIndex': initialSelectedIndex,
      'multipleSelection': multipleSelection,
      'initialSelectedIndices': initialSelectedIndices,
      'showCheckboxes': showCheckboxes,
      'checkboxSize': checkboxSize,
      'showScrollbar': showScrollbar,
      'scrollbarThickness': scrollbarThickness,
      'scrollbarRadius': scrollbarRadius,
      'alwaysShowScrollbar': alwaysShowScrollbar,
      'maxHeight': maxHeight,
      'shrinkWrap': shrinkWrap,

      // Colors
      'titleColor': titleColor != null ? '#${titleColor!.toHexString()}' : null,
      'itemColor': itemColor != null ? '#${itemColor!.toHexString()}' : null,
      'backgroundColor': backgroundColor != null ? '#${backgroundColor!.toHexString()}' : null,
      'titleBackgroundColor': titleBackgroundColor != null ? '#${titleBackgroundColor!.toHexString()}' : null,
      'itemBackgroundColor': itemBackgroundColor != null ? '#${itemBackgroundColor!.toHexString()}' : null,
      'borderColor': borderColor != null ? '#${borderColor!.toHexString()}' : null,
      'titleBorderColor': titleBorderColor != null ? '#${titleBorderColor!.toHexString()}' : null,
      'itemBorderColor': itemBorderColor != null ? '#${itemBorderColor!.toHexString()}' : null,
      'shadowColor': shadowColor != null ? '#${shadowColor!.toHexString()}' : null,
      'titleShadowColor': titleShadowColor != null ? '#${titleShadowColor!.toHexString()}' : null,
      'itemShadowColor': itemShadowColor != null ? '#${itemShadowColor!.toHexString()}' : null,
      'bulletColor': bulletColor != null ? '#${bulletColor!.toHexString()}' : null,
      'numberColor': numberColor != null ? '#${numberColor!.toHexString()}' : null,
      'selectedItemColor': selectedItemColor != null ? '#${selectedItemColor!.toHexString()}' : null,
      'selectedItemBackgroundColor': selectedItemBackgroundColor != null ? '#${selectedItemBackgroundColor!.toHexString()}' : null,
      'checkboxColor': checkboxColor != null ? '#${checkboxColor!.toHexString()}' : null,
      'dividerColor': dividerColor != null ? '#${dividerColor!.toHexString()}' : null,
      'titleDividerColor': titleDividerColor != null ? '#${titleDividerColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Advanced properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonListLabelConfig': useJsonListLabelConfig,
    };
  }

  @override
  ListLabelWidgetState createState() => ListLabelWidgetState();
}

/// The type of bullet to use for the items.
enum BulletType {
  /// A dot bullet.
  dot,

  /// A dash bullet.
  dash,

  /// A square bullet.
  square,

  /// A diamond bullet.
  diamond,

  /// A circle bullet.
  circle,

  /// An arrow bullet.
  arrow,

  /// A custom icon bullet.
  icon,

  /// No bullet.
  none,
}

/// The style of the numbers for numbered lists.
enum NumberStyle {
  /// Decimal numbers (1, 2, 3, ...).
  decimal,

  /// Lowercase letters (a, b, c, ...).
  lowerAlpha,

  /// Uppercase letters (A, B, C, ...).
  upperAlpha,

  /// Lowercase Roman numerals (i, ii, iii, ...).
  lowerRoman,

  /// Uppercase Roman numerals (I, II, III, ...).
  upperRoman,
}

class ListLabelWidgetState extends State<ListLabelWidget> with SingleTickerProviderStateMixin {
  late int? _selectedIndex;
  late List<int> _selectedIndices;
  bool _isHovered = false;
  final bool _isFocused = false;

  // Animation controller for animated effects
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialSelectedIndex;
    _selectedIndices = widget.initialSelectedIndices?.toList() ?? [];

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void didUpdateWidget(ListLabelWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget containerWidget = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.title != null) _buildTitle(),
          if (widget.title != null && widget.showTitleDivider) _buildTitleDivider(),
          if (widget.title != null) SizedBox(height: 4.0), // Fixed small spacing for compact look
          _buildItemsList(),
        ],
      ),
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      containerWidget = FadeTransition(
        opacity: _animation,
        child: containerWidget,
      );
    }

    // Apply advanced interaction properties
    if (widget.onHover != null || widget.onFocus != null ||
        widget.onDoubleTap != null) {

      containerWidget = MouseRegion(
        onEnter: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = true;
            });
            widget.onHover!(true);
          }
        },
        onExit: (event) {
          if (widget.onHover != null) {
            setState(() {
              _isHovered = false;
            });
            widget.onHover!(false);
          }
        },
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onDoubleTap: widget.onDoubleTap != null ? () {
            // Execute onDoubleTap callback if defined in JSON
            if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onDoubleTap')) {
              _executeJsonCallback('onDoubleTap');
            }

            // Call standard callback
            widget.onDoubleTap!();
          } : null,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: widget.onFocus,
            child: containerWidget,
          ),
        ),
      );
    }

    // Add tooltip if needed
    if (widget.tooltip != null) {
      containerWidget = Tooltip(
        message: widget.tooltip!,
        child: containerWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      containerWidget = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: containerWidget,
      );
    }

    return containerWidget;
  }

  Widget _buildTitle() {
    // Process text transformations
    String displayTitle = widget.title!;
    if (widget.titleUppercase) {
      displayTitle = displayTitle.toUpperCase();
    } else if (widget.titleLowercase) {
      displayTitle = displayTitle.toLowerCase();
    } else if (widget.titleCapitalize) {
      displayTitle = displayTitle.split(' ').map((word) {
        if (word.isEmpty) return word;
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }).join(' ');
    }

    // Create text style with better defaults - force our styling over theme
    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else if (screenWidth >= 768) {
        return 12.0; // Small
      } else {
        return 10.0; // Extra Small (fallback for very small screens)
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(screenWidth);

    TextStyle effectiveTitleStyle = widget.titleStyle ??
        TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w500, // Default medium weight for titles
          fontSize: responsiveTitleFontSize, // Responsive font size
          color: Colors.black, // Default readable color
        );

    // Debug: Print the styling information


    // Apply text style properties
    if (widget.titleFontSize != null) {
      effectiveTitleStyle = effectiveTitleStyle.copyWith(fontSize: widget.titleFontSize);
    }

    // Only apply font weight if explicitly provided (not default values)
    if (widget.titleFontWeight != null) {
      effectiveTitleStyle = effectiveTitleStyle.copyWith(fontWeight: widget.titleFontWeight);
    } else if (widget.titleBold) {
      effectiveTitleStyle = effectiveTitleStyle.copyWith(fontWeight: FontWeight.bold);
    }

    if (widget.titleFontStyle != null || widget.titleItalic) {
      effectiveTitleStyle = effectiveTitleStyle.copyWith(
        fontStyle: widget.titleFontStyle ?? (widget.titleItalic ? FontStyle.italic : null),
      );
    }

    if (widget.titleColor != null) {
      effectiveTitleStyle = effectiveTitleStyle.copyWith(color: widget.titleColor);
    }

    // Debug: Print final styling
    // print('DEBUG: Final title style: $effectiveTitleStyle');

    // Create the title widget
    Widget titleWidget = Text(
      displayTitle,
      style: effectiveTitleStyle,
      textAlign: widget.titleAlign,
      maxLines: widget.titleMaxLines,
      overflow: widget.titleOverflow ? TextOverflow.ellipsis : TextOverflow.visible,
    );

    // Apply simple container styling for clean look
    Widget titleContainer = Container(
      width: double.infinity,
      padding: widget.titlePadding,
      child: titleWidget,
    );

    // Add interactivity if needed
    if (widget.onTitleTap != null || widget.onTitleLongPress != null ||
        (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
         (widget.jsonCallbacks!.containsKey('onTitleTap') ||
          widget.jsonCallbacks!.containsKey('onTitleLongPress')))) {

      titleContainer = InkWell(
        onTap: widget.onTitleTap != null ||
              (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
               widget.jsonCallbacks!.containsKey('onTitleTap'))
            ? () {
                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onTitleTap')) {
                  _executeJsonCallback('onTitleTap');
                }

                if (widget.onTitleTap != null) {
                  widget.onTitleTap!();
                }
              }
            : null,
        onLongPress: widget.onTitleLongPress != null ||
                    (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                     widget.jsonCallbacks!.containsKey('onTitleLongPress'))
            ? () {
                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onTitleLongPress')) {
                  _executeJsonCallback('onTitleLongPress');
                }

                if (widget.onTitleLongPress != null) {
                  widget.onTitleLongPress!();
                }
              }
            : null,
        borderRadius: BorderRadius.circular(widget.titleBorderRadius),
        child: titleContainer,
      );
    }

    return titleContainer;
  }

  Widget _buildTitleDivider() {
    return Divider(
      color: widget.titleDividerColor ?? Theme.of(context).dividerColor,
      thickness: widget.titleDividerThickness,
      indent: widget.titleDividerIndent,
      endIndent: widget.titleDividerEndIndent,
    );
  }

  Widget _buildItemsList() {
    final listView = ListView.builder(
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics ?? (widget.maxHeight != null ? const ClampingScrollPhysics() : const NeverScrollableScrollPhysics()),
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        return _buildItem(index);
      },
    );

    if (widget.maxHeight != null) {
      return Container(
        constraints: BoxConstraints(maxHeight: widget.maxHeight!),
        child: widget.showScrollbar
            ? Scrollbar(
                thickness: widget.scrollbarThickness,
                radius: Radius.circular(widget.scrollbarRadius),
                thumbVisibility: widget.alwaysShowScrollbar,
                child: listView,
              )
            : listView,
      );
    }

    return listView;
  }

  Widget _buildItem(int index) {
    final item = widget.items[index];
    final isSelected = widget.multipleSelection
        ? _selectedIndices.contains(index)
        : _selectedIndex == index;

    // Process text transformations
    String displayItem = item;
    if (widget.itemUppercase) {
      displayItem = displayItem.toUpperCase();
    } else if (widget.itemLowercase) {
      displayItem = displayItem.toLowerCase();
    } else if (widget.itemCapitalize) {
      displayItem = displayItem.split(' ').map((word) {
        if (word.isEmpty) return word;
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }).join(' ');
    }

    // Get responsive font size for items (slightly smaller than title)
    double getResponsiveItemFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large (2px smaller than title)
      } else if (screenWidth >= 1440) {
        return 16.0; // Large (2px smaller than title)
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium (2px smaller than title)
      } else if (screenWidth >= 768) {
        return 12.0; // Small (2px smaller than title)
      } else {
        return 9.0; // Extra Small (1px smaller than title)
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveItemFontSize = getResponsiveItemFontSize(screenWidth);

    // Create text style with better defaults - force our styling over theme
    TextStyle effectiveItemStyle = widget.itemStyle ??
        TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w500, // Default medium weight for items
          fontSize: responsiveItemFontSize, // Responsive font size
          color: Colors.black, // Default readable color
        );

    // Apply text style properties
    if (widget.itemFontSize != null) {
      effectiveItemStyle = effectiveItemStyle.copyWith(fontSize: widget.itemFontSize);
    }

    if (widget.itemFontWeight != null || widget.itemBold) {
      effectiveItemStyle = effectiveItemStyle.copyWith(
        fontWeight: widget.itemFontWeight ?? (widget.itemBold ? FontWeight.bold : null),
      );
    }

    if (widget.itemFontStyle != null || widget.itemItalic) {
      effectiveItemStyle = effectiveItemStyle.copyWith(
        fontStyle: widget.itemFontStyle ?? (widget.itemItalic ? FontStyle.italic : null),
      );
    }

    if (widget.itemColor != null) {
      effectiveItemStyle = effectiveItemStyle.copyWith(color: widget.itemColor);
    }

    // Apply selection styling
    if (isSelected && widget.selectable) {
      if (widget.selectedItemColor != null) {
        effectiveItemStyle = effectiveItemStyle.copyWith(color: widget.selectedItemColor);
      }
    }

    // Create the item text widget
    Widget itemTextWidget = Text(
      displayItem,
      style: effectiveItemStyle,
      textAlign: widget.itemAlign,
      maxLines: widget.itemMaxLines,
      overflow: widget.itemOverflow ? TextOverflow.ellipsis : TextOverflow.visible,
    );

    // Create the bullet or number widget
    Widget? prefixWidget;
    if (widget.numbered) {
      String numberText;
      switch (widget.numberStyle) {
        case NumberStyle.decimal:
          numberText = widget.numberFormat.replaceAll('%d', '${index + 1}');
          break;
        case NumberStyle.lowerAlpha:
          numberText = widget.numberFormat.replaceAll('%d', String.fromCharCode(97 + (index % 26)));
          break;
        case NumberStyle.upperAlpha:
          numberText = widget.numberFormat.replaceAll('%d', String.fromCharCode(65 + (index % 26)));
          break;
        case NumberStyle.lowerRoman:
          numberText = widget.numberFormat.replaceAll('%d', _toRoman(index + 1).toLowerCase());
          break;
        case NumberStyle.upperRoman:
          numberText = widget.numberFormat.replaceAll('%d', _toRoman(index + 1));
          break;
      }

      prefixWidget = Text(
        numberText,
        style: effectiveItemStyle.copyWith(
          color: widget.numberColor ?? effectiveItemStyle.color,
        ),
      );
    } else if (widget.bulletType != BulletType.none) {
      switch (widget.bulletType) {
        case BulletType.dot:
          prefixWidget = Container(
            width: 4.0, // Fixed small size for consistent look
            height: 4.0,
            decoration: BoxDecoration(
              color: widget.bulletColor ?? effectiveItemStyle.color ?? Colors.black87,
              shape: BoxShape.circle,
            ),
          );
          break;
        case BulletType.dash:
          prefixWidget = Container(
            width: widget.bulletSize * 1.5,
            height: widget.bulletSize / 3,
            decoration: BoxDecoration(
              color: widget.bulletColor ?? effectiveItemStyle.color,
              borderRadius: BorderRadius.circular(widget.bulletSize / 6),
            ),
          );
          break;
        case BulletType.square:
          prefixWidget = Container(
            width: widget.bulletSize,
            height: widget.bulletSize,
            decoration: BoxDecoration(
              color: widget.bulletColor ?? effectiveItemStyle.color,
              shape: BoxShape.rectangle,
            ),
          );
          break;
        case BulletType.diamond:
          prefixWidget = Transform.rotate(
            angle: 0.785398, // 45 degrees in radians
            child: Container(
              width: widget.bulletSize,
              height: widget.bulletSize,
              decoration: BoxDecoration(
                color: widget.bulletColor ?? effectiveItemStyle.color,
                shape: BoxShape.rectangle,
              ),
            ),
          );
          break;
        case BulletType.circle:
          prefixWidget = Container(
            width: widget.bulletSize,
            height: widget.bulletSize,
            decoration: BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
              border: Border.all(
                color: widget.bulletColor ?? effectiveItemStyle.color!,
                width: widget.bulletSize / 4,
              ),
            ),
          );
          break;
        case BulletType.arrow:
          prefixWidget = Icon(
            Icons.arrow_right,
            size: widget.bulletSize * 2,
            color: widget.bulletColor ?? effectiveItemStyle.color,
          );
          break;
        case BulletType.icon:
          prefixWidget = Icon(
            widget.bulletIcon ?? Icons.circle,
            size: widget.bulletSize * 1.5,
            color: widget.bulletColor ?? effectiveItemStyle.color,
          );
          break;
        case BulletType.none:
          prefixWidget = null;
          break;
      }
    }

    // Create the checkbox widget
    Widget? checkboxWidget;
    if (widget.showCheckboxes) {
      checkboxWidget = SizedBox(
        width: widget.checkboxSize,
        height: widget.checkboxSize,
        child: Checkbox(
          value: widget.multipleSelection
              ? _selectedIndices.contains(index)
              : _selectedIndex == index,
          onChanged: (value) {
            _handleItemSelection(index);
          },
          activeColor: widget.checkboxColor,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      );
    }

    // Combine the prefix, checkbox, and text widgets
    Widget rowWidget = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (checkboxWidget != null) ...[
          checkboxWidget,
          SizedBox(width: widget.bulletSpacing),
        ],
        if (prefixWidget != null) ...[
          SizedBox(
            width: 12, // Smaller fixed width for tighter alignment
            child: Align(
              alignment: Alignment.centerLeft,
              child: prefixWidget,
            ),
          ),
          SizedBox(width: 6), // Reduced spacing for compact look
        ],
        Expanded(child: itemTextWidget),
      ],
    );

    // Apply simple container styling for clean look
    Widget itemContainer = Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 1.0), // Minimal padding for compact look
      decoration: BoxDecoration(
        color: isSelected && widget.selectable
            ? widget.selectedItemBackgroundColor
            : null, // Remove default background for cleaner look
      ),
      child: rowWidget,
    );

    // Add interactivity if needed
    if (widget.selectable || widget.onItemTap != null || widget.onItemLongPress != null) {
      itemContainer = InkWell(
        onTap: () {
          if (widget.selectable) {
            _handleItemSelection(index);
          }

          // Execute JSON callback if defined
          if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onItemTap')) {
            _executeJsonCallback('onItemTap', index);
          }

          if (widget.onItemTap != null) {
            widget.onItemTap!(index);
          }
        },
        onLongPress: widget.onItemLongPress != null ||
                    (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                     widget.jsonCallbacks!.containsKey('onItemLongPress'))
            ? () {
                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onItemLongPress')) {
                  _executeJsonCallback('onItemLongPress', index);
                }

                if (widget.onItemLongPress != null) {
                  widget.onItemLongPress!(index);
                }
              }
            : null,
        borderRadius: BorderRadius.circular(widget.itemBorderRadius),
        child: itemContainer,
      );
    }

    return itemContainer;
  }

  void _handleItemSelection(int index) {
    setState(() {
      if (widget.multipleSelection) {
        if (_selectedIndices.contains(index)) {
          _selectedIndices.remove(index);
        } else {
          _selectedIndices.add(index);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onMultipleSelectionChanged')) {
          _executeJsonCallback('onMultipleSelectionChanged', _selectedIndices);
        }

        // Call standard callback
        if (widget.onMultipleSelectionChanged != null) {
          widget.onMultipleSelectionChanged!(_selectedIndices);
        }
      } else {
        _selectedIndex = _selectedIndex == index ? null : index;

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onSelectionChanged')) {
          _executeJsonCallback('onSelectionChanged', _selectedIndex);
        }

        // Call standard callback
        if (widget.onSelectionChanged != null) {
          widget.onSelectionChanged!(_selectedIndex);
        }
      }
    });
  }

  String _toRoman(int number) {
    if (number <= 0 || number > 3999) {
      return number.toString();
    }

    final List<String> romanNumerals = [
      'I', 'IV', 'V', 'IX', 'X', 'XL', 'L', 'XC', 'C', 'CD', 'D', 'CM', 'M'
    ];
    final List<int> values = [
      1, 4, 5, 9, 10, 40, 50, 90, 100, 400, 500, 900, 1000
    ];

    String result = '';
    int i = 12;

    while (number > 0) {
      while (number >= values[i]) {
        number -= values[i];
        result += romanNumerals[i];
      }
      i--;
    }

    return result;
  }
}
