
import 'package:flutter/material.dart';

class BooleanWidget extends StatefulWidget {
  final bool initialValue;
  final Color activeColor;
  final Color inactiveColor;
  final Color? thumbColor;
  final Color? trackColor;
  final double width;
  final double height;
  final String? label;
  final bool showStatusText;
  final TextAlign labelPosition;
  final bool isRounded;
  final ValueChanged<bool>? onChanged;
  final bool? testValue; // For testing purposes only

  const BooleanWidget({
    super.key,
    this.initialValue = false,
    this.activeColor = Colors.blue, 
    this.inactiveColor = Colors.grey,
    this.thumbColor,
    this.trackColor,
    this.width = 60.0,
    this.height = 30.0,
    this.label,
    this.showStatusText = false,
    this.labelPosition = TextAlign.start,
    this.isRounded = true,
    this.onChanged,
    this.testValue,
  });

  /// Creates a BooleanWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the BooleanWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": true,
  ///   "activeColor": "blue",
  ///   "inactiveColor": "grey",
  ///   "thumbColor": "white",
  ///   "trackColor": "#E0E0E0",
  ///   "width": 60.0,
  ///   "height": 30.0,
  ///   "label": "Wi-Fi",
  ///   "showStatusText": true,
  ///   "labelPosition": "end",
  ///   "isRounded": true
  /// }
  /// ```
  factory BooleanWidget.fromJson(Map<String, dynamic> json) {
    // Parse label position
    TextAlign labelPosition = TextAlign.start;
    if (json['labelPosition'] != null) {
      switch (json['labelPosition'].toString().toLowerCase()) {
        case 'end':
        case 'right':
        case 'trailing':
          labelPosition = TextAlign.end;
          break;
        case 'start':
        case 'left':
        case 'leading':
          labelPosition = TextAlign.start;
          break;
      }
    }

    return BooleanWidget(
      initialValue: json['initialValue'] ?? false,
      activeColor: _colorFromJson(json['activeColor']) ?? Colors.blue,
      inactiveColor: _colorFromJson(json['inactiveColor']) ?? Colors.grey,
      thumbColor: _colorFromJson(json['thumbColor']),
      trackColor: _colorFromJson(json['trackColor']),
      width: (json['width'] as num?)?.toDouble() ?? 60.0,
      height: (json['height'] as num?)?.toDouble() ?? 30.0,
      label: json['label'] as String?,
      showStatusText: json['showStatusText'] ?? false,
      labelPosition: labelPosition,
      isRounded: json['isRounded'] ?? true,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
    );
  }

  /// Converts the BooleanWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String labelPositionString = 'start';
    if (labelPosition == TextAlign.end) {
      labelPositionString = 'end';
    }

    return {
      'initialValue': initialValue,
      'activeColor': _colorToJson(activeColor),
      'inactiveColor': _colorToJson(inactiveColor),
      if (thumbColor != null) 'thumbColor': _colorToJson(thumbColor!),
      if (trackColor != null) 'trackColor': _colorToJson(trackColor!),
      'width': width,
      'height': height,
      if (label != null) 'label': label,
      'showStatusText': showStatusText,
      'labelPosition': labelPositionString,
      'isRounded': isRounded,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
    final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
    final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');

    return '#$r$g$b';
  }

  @override
  BooleanWidgetState createState() => BooleanWidgetState();
}

class BooleanWidgetState extends State<BooleanWidget> {
  late bool _isTrue;

  @override
  void initState() {
    super.initState();
    // Use test value if provided (for testing purposes)
    if (widget.testValue != null) {
      _isTrue = widget.testValue!;
    } else {
      _isTrue = widget.initialValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the widget to use based on the configuration
    Widget toggleWidget;

    // Create the switch/toggle widget
    if (widget.isRounded) {
      // Use Switch for rounded toggle
      toggleWidget = Switch(
        value: _isTrue,
        activeColor: widget.activeColor,
        inactiveThumbColor: widget.thumbColor ?? widget.inactiveColor,
        activeTrackColor: widget.trackColor ?? widget.activeColor.withAlpha(128),
        inactiveTrackColor: widget.trackColor ?? Colors.grey.shade300,
        onChanged: (bool value) {
          setState(() {
            _isTrue = value;
          });
          widget.onChanged?.call(value);
        },
      );
    } else {
      // Use Checkbox for square toggle
      toggleWidget = Checkbox(
        value: _isTrue,
        activeColor: widget.activeColor,
        checkColor: widget.thumbColor ?? Colors.white,
        side: BorderSide(color: widget.inactiveColor),
        onChanged: (bool? value) {
          if (value != null) {
            setState(() {
              _isTrue = value;
            });
            widget.onChanged?.call(value);
          }
        },
      );
    }

    // Create the status text if needed
    Widget? statusText;
    if (widget.showStatusText) {
      statusText = Container(
        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: _isTrue ? widget.activeColor.withAlpha(50) : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          _isTrue ? 'ON' : 'OFF',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: _isTrue ? widget.activeColor : Colors.grey.shade700,
          ),
        ),
      );
    }

    // Create the label if provided
    Widget? labelWidget;
    if (widget.label != null) {
      labelWidget = Text(
        widget.label!,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
      );
    }

    // Arrange the widgets based on label position
    List<Widget> rowChildren = [];

    if (widget.labelPosition == TextAlign.start) {
      // Label on left (leading)
      if (labelWidget != null) rowChildren.add(labelWidget);
      rowChildren.add(Spacer());
      rowChildren.add(toggleWidget);
      if (statusText != null) {
        rowChildren.add(SizedBox(width: 8));
        rowChildren.add(statusText);
      }
    } else {
      // Label on right (trailing)
      rowChildren.add(toggleWidget);
      if (statusText != null) {
        rowChildren.add(SizedBox(width: 8));
        rowChildren.add(statusText);
      }
      rowChildren.add(Spacer());
      if (labelWidget != null) rowChildren.add(labelWidget);
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: rowChildren,
          ),
        ],
      ),
    );
  }
}
