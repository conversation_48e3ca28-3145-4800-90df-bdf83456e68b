import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A comprehensive geolocation widget that provides advanced location services.
///
/// This widget offers a rich set of features for geolocation services including:
/// - Current location detection with configurable accuracy
/// - Reverse geocoding (coordinates to address)
/// - Forward geocoding (address to coordinates)
/// - Distance calculation between points
/// - Customizable map display
/// - Location tracking with history
/// - Geofencing capabilities
/// - Heading and bearing information
/// - Speed and altitude data
/// - Customizable UI with various display modes
class GeolocationWidget extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool isReadOnly;
  final bool isDisabled;

  // Display properties
  final GeolocationDisplayMode displayMode;
  final double? width;
  final double? height;
  final Color backgroundColor;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Text properties
  final Color textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;

  // Map properties
  final double? initialLatitude;
  final double? initialLongitude;
  final double mapZoom;
  final bool showMapControls;
  final MapType mapType;
  final bool enableMapGestures;
  final Set<Marker>? markers;
  final bool showUserLocation;
  final bool showTraffic;
  final bool showBuildings;
  final bool showPOI;
  final bool showIndoorMap;
  final bool showMapScale;
  final bool showMapCompass;
  final bool showMapToolbar;

  // Address properties
  final String? initialAddress;
  final bool showAddressField;
  final bool enableAddressSearch;
  final bool showCoordinates;
  final String addressHint;
  final bool formatAddress;
  final bool showAddressComponents;

  // Current location properties
  final bool showCurrentLocationButton;
  final bool autoDetectLocation;
  final LocationAccuracy locationAccuracy;
  final bool continuousLocationUpdates;
  final int locationUpdateInterval;
  final bool showLocationAccuracy;

  // Distance properties
  final bool showDistance;
  final bool showDistanceUnit;
  final DistanceUnit distanceUnit;
  final bool enableDistanceCalculation;
  final double? targetLatitude;
  final double? targetLongitude;

  // Geofencing properties
  final bool enableGeofencing;
  final List<GeofenceCircle>? geofenceCircles;
  final List<GeofencePolygon>? geofencePolygons;
  final bool showGeofenceAlerts;

  // History properties
  final bool trackLocationHistory;
  final int maxHistoryItems;
  final bool showLocationHistory;
  final bool showHistoryPath;
  final Color historyPathColor;
  final double historyPathWidth;

  // Additional data properties
  final bool showAltitude;
  final bool showSpeed;
  final bool showHeading;
  final bool showTimestamp;
  final bool showBattery;
  final bool showWeather;

  // Button properties
  final String currentLocationButtonText;
  final String searchButtonText;
  final String clearButtonText;
  final Color buttonColor;
  final Color buttonTextColor;
  final bool showClearButton;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Callback functions
  final Function(double latitude, double longitude)? onLocationChanged;
  final Function(String address)? onAddressChanged;
  final Function(Position position)? onCurrentLocationDetected;
  final Function(double distance)? onDistanceCalculated;
  final Function(GeofenceStatus status, String identifier)? onGeofenceStatusChanged;
  final Function(String error)? onError;

  const GeolocationWidget({
    super.key,
    this.isRequired = false,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.displayMode = GeolocationDisplayMode.mapAndAddress,
    this.width,
    this.height,
    this.backgroundColor = Colors.white,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = true,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = const EdgeInsets.all(0.0),
    this.textColor = Colors.black,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.initialLatitude,
    this.initialLongitude,
    this.mapZoom = 14.0,
    this.showMapControls = true,
    this.mapType = MapType.normal,
    this.enableMapGestures = true,
    this.markers,
    this.showUserLocation = true,
    this.showTraffic = false,
    this.showBuildings = false,
    this.showPOI = true,
    this.showIndoorMap = false,
    this.showMapScale = false,
    this.showMapCompass = false,
    this.showMapToolbar = false,
    this.initialAddress,
    this.showAddressField = true,
    this.enableAddressSearch = true,
    this.showCoordinates = false,
    this.addressHint = 'Enter address',
    this.formatAddress = true,
    this.showAddressComponents = false,
    this.showCurrentLocationButton = true,
    this.autoDetectLocation = false,
    this.locationAccuracy = LocationAccuracy.high,
    this.continuousLocationUpdates = false,
    this.locationUpdateInterval = 5000,
    this.showLocationAccuracy = false,
    this.showDistance = false,
    this.showDistanceUnit = true,
    this.distanceUnit = DistanceUnit.kilometers,
    this.enableDistanceCalculation = false,
    this.targetLatitude,
    this.targetLongitude,
    this.enableGeofencing = false,
    this.geofenceCircles,
    this.geofencePolygons,
    this.showGeofenceAlerts = true,
    this.trackLocationHistory = false,
    this.maxHistoryItems = 10,
    this.showLocationHistory = false,
    this.showHistoryPath = false,
    this.historyPathColor = Colors.blue,
    this.historyPathWidth = 2.0,
    this.showAltitude = false,
    this.showSpeed = false,
    this.showHeading = false,
    this.showTimestamp = false,
    this.showBattery = false,
    this.showWeather = false,
    this.currentLocationButtonText = 'Current Location',
    this.searchButtonText = 'Search',
    this.clearButtonText = 'Clear',
    this.buttonColor = Colors.blue,
    this.buttonTextColor = Colors.white,
    this.showClearButton = true,
    this.showIcon = true,
    this.icon = Icons.location_on,
    this.onLocationChanged,
    this.onAddressChanged,
    this.onCurrentLocationDetected,
    this.onDistanceCalculated,
    this.onGeofenceStatusChanged,
    this.onError,
  });

  @override
  State<GeolocationWidget> createState() => _GeolocationWidgetState();
}

/// Enum defining the display modes for the geolocation widget
enum GeolocationDisplayMode {
  /// Shows both map and address field
  mapAndAddress,

  /// Shows only the map
  mapOnly,

  /// Shows only the address field
  addressOnly,

  /// Shows coordinates only
  coordinatesOnly,

  /// Shows a compact view with essential information
  compact,

  /// Shows a detailed view with all available information
  detailed,
}

/// Enum defining the distance units
enum DistanceUnit {
  /// Distance in meters
  meters,

  /// Distance in kilometers
  kilometers,

  /// Distance in miles
  miles,

  /// Distance in feet
  feet,

  /// Distance in nautical miles
  nauticalMiles,
}

/// Class representing a circular geofence
class GeofenceCircle {
  final String identifier;
  final double latitude;
  final double longitude;
  final double radius;
  final Color color;

  const GeofenceCircle({
    required this.identifier,
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.color = Colors.red,
  });
}

/// Class representing a polygon geofence
class GeofencePolygon {
  final String identifier;
  final List<LatLng> points;
  final Color color;

  const GeofencePolygon({
    required this.identifier,
    required this.points,
    this.color = Colors.blue,
  });
}

/// Enum defining the geofence status
enum GeofenceStatus {
  /// User entered the geofence
  enter,

  /// User exited the geofence
  exit,

  /// User is dwelling inside the geofence
  dwell,
}

class _GeolocationWidgetState extends State<GeolocationWidget> {
  // Controllers
  final TextEditingController _addressController = TextEditingController();
  GoogleMapController? _mapController;
  StreamSubscription<Position>? _positionStreamSubscription;

  // Location data
  double? _latitude;
  double? _longitude;
  String? _address;
  double? _accuracy;
  double? _altitude;
  double? _speed;
  double? _heading;
  DateTime? _timestamp;
  bool _isLoading = false;
  String? _errorMessage;

  // Markers and paths
  Set<Marker> _markers = {};
  List<LatLng> _historyPoints = [];

  // Geofencing
  final Map<String, GeofenceStatus> _geofenceStatuses = {};

  @override
  void initState() {
    super.initState();

    // Initialize address controller
    if (widget.initialAddress != null) {
      _addressController.text = widget.initialAddress!;
      _address = widget.initialAddress;
    }

    // Initialize coordinates
    _latitude = widget.initialLatitude;
    _longitude = widget.initialLongitude;

    // Initialize markers
    if (widget.markers != null) {
      _markers = widget.markers!;
    } else if (_latitude != null && _longitude != null) {
      _markers = {
        Marker(
          markerId: const MarkerId('initial_location'),
          position: LatLng(_latitude!, _longitude!),
        ),
      };
    }

    // Auto-detect location if enabled
    if (widget.autoDetectLocation) {
      _getCurrentLocation();
    }

    // Start continuous location updates if enabled
    if (widget.continuousLocationUpdates) {
      _startLocationUpdates();
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _positionStreamSubscription?.cancel();
    super.dispose();
  }

  /// Request location permissions
  Future<bool> _requestLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      setState(() {
        _errorMessage = 'Location services are disabled';
        _isLoading = false;
      });

      if (widget.onError != null) {
        widget.onError!('Location services are disabled');
      }

      return false;
    }

    // Check location permission
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        setState(() {
          _errorMessage = 'Location permission denied';
          _isLoading = false;
        });

        if (widget.onError != null) {
          widget.onError!('Location permission denied');
        }

        return false;
      }
    }

    // Check if permission is permanently denied
    if (permission == LocationPermission.deniedForever) {
      setState(() {
        _errorMessage = 'Location permission permanently denied';
        _isLoading = false;
      });

      if (widget.onError != null) {
        widget.onError!('Location permission permanently denied');
      }

      return false;
    }

    return true;
  }

  /// Get current location
  Future<void> _getCurrentLocation() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Request permission
    bool hasPermission = await _requestLocationPermission();
    if (!hasPermission) return;

    try {
      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: widget.locationAccuracy,
      );

      // Update state
      setState(() {
        _latitude = position.latitude;
        _longitude = position.longitude;
        _accuracy = position.accuracy;
        _altitude = position.altitude;
        _speed = position.speed;
        _heading = position.heading;
        _timestamp = DateTime.now();
        _markers = {
          Marker(
            markerId: const MarkerId('current_location'),
            position: LatLng(position.latitude, position.longitude),
          ),
        };

        // Add to history if tracking is enabled
        if (widget.trackLocationHistory) {
          _addToHistory(LatLng(position.latitude, position.longitude));
        }

        _isLoading = false;
      });

      // Update map camera
      _mapController?.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(position.latitude, position.longitude),
          widget.mapZoom,
        ),
      );

      // Get address from coordinates
      _getAddressFromCoordinates(position.latitude, position.longitude);

      // Check geofences if enabled
      if (widget.enableGeofencing) {
        _checkGeofences(position.latitude, position.longitude);
      }

      // Calculate distance if enabled
      if (widget.enableDistanceCalculation &&
          widget.targetLatitude != null &&
          widget.targetLongitude != null) {
        _calculateDistance(
          position.latitude,
          position.longitude,
          widget.targetLatitude!,
          widget.targetLongitude!
        );
      }

      // Call callback
      if (widget.onCurrentLocationDetected != null) {
        widget.onCurrentLocationDetected!(position);
      }

      if (widget.onLocationChanged != null) {
        widget.onLocationChanged!(position.latitude, position.longitude);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error getting location: $e';
      });

      if (widget.onError != null) {
        widget.onError!('Error getting location: $e');
      }
    }
  }

  /// Start continuous location updates
  void _startLocationUpdates() async {
    // Request permission
    bool hasPermission = await _requestLocationPermission();
    if (!hasPermission) return;

    // Cancel existing subscription if any
    await _positionStreamSubscription?.cancel();

    // Create location options
    final locationSettings = LocationSettings(
      accuracy: widget.locationAccuracy,
      distanceFilter: 10, // minimum distance (in meters) to travel before updates
      timeLimit: Duration(milliseconds: widget.locationUpdateInterval),
    );

    // Subscribe to position updates
    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (Position position) {
        setState(() {
          _latitude = position.latitude;
          _longitude = position.longitude;
          _accuracy = position.accuracy;
          _altitude = position.altitude;
          _speed = position.speed;
          _heading = position.heading;
          _timestamp = DateTime.now();

          // Add to history if tracking is enabled
          if (widget.trackLocationHistory) {
            _addToHistory(LatLng(position.latitude, position.longitude));
          }
        });

        // Update map if controller exists
        _mapController?.animateCamera(
          CameraUpdate.newLatLng(
            LatLng(position.latitude, position.longitude),
          ),
        );

        // Update marker
        setState(() {
          _markers = {
            ..._markers.where((m) => m.markerId.value != 'current_location'),
            Marker(
              markerId: const MarkerId('current_location'),
              position: LatLng(position.latitude, position.longitude),
            ),
          };
        });

        // Check geofences if enabled
        if (widget.enableGeofencing) {
          _checkGeofences(position.latitude, position.longitude);
        }

        // Calculate distance if enabled
        if (widget.enableDistanceCalculation &&
            widget.targetLatitude != null &&
            widget.targetLongitude != null) {
          _calculateDistance(
            position.latitude,
            position.longitude,
            widget.targetLatitude!,
            widget.targetLongitude!
          );
        }

        // Call callback
        if (widget.onLocationChanged != null) {
          widget.onLocationChanged!(position.latitude, position.longitude);
        }
      },
      onError: (e) {
        setState(() {
          _errorMessage = 'Location update error: $e';
        });

        if (widget.onError != null) {
          widget.onError!('Location update error: $e');
        }
      },
    );
  }

  /// Stop continuous location updates
  void _stopLocationUpdates() async {
    await _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
  }

  /// Get address from coordinates (reverse geocoding)
  Future<void> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;

        // Format address based on available components
        String formattedAddress = widget.formatAddress
            ? _formatAddress(place)
            : '${place.street}, ${place.locality}, ${place.postalCode}, ${place.country}';

        setState(() {
          _address = formattedAddress;
          _addressController.text = formattedAddress;
        });

        if (widget.onAddressChanged != null) {
          widget.onAddressChanged!(formattedAddress);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error getting address: $e';
      });

      if (widget.onError != null) {
        widget.onError!('Error getting address: $e');
      }
    }
  }

  /// Format address from placemark
  String _formatAddress(Placemark place) {
    List<String> addressParts = [];

    if (place.name != null && place.name!.isNotEmpty) {
      addressParts.add(place.name!);
    }

    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (place.postalCode != null && place.postalCode!.isNotEmpty) {
      addressParts.add(place.postalCode!);
    }

    if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    if (place.country != null && place.country!.isNotEmpty) {
      addressParts.add(place.country!);
    }

    return addressParts.join(', ');
  }

  /// Get coordinates from address (forward geocoding)
  Future<void> _getCoordinatesFromAddress(String address) async {
    if (address.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        Location location = locations.first;

        setState(() {
          _latitude = location.latitude;
          _longitude = location.longitude;
          _markers = {
            Marker(
              markerId: const MarkerId('searched_location'),
              position: LatLng(location.latitude, location.longitude),
            ),
          };
          _isLoading = false;
        });

        // Update map camera
        _mapController?.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(location.latitude, location.longitude),
            widget.mapZoom,
          ),
        );

        if (widget.onLocationChanged != null) {
          widget.onLocationChanged!(location.latitude, location.longitude);
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Address not found';
      });

      if (widget.onError != null) {
        widget.onError!('Address not found: $e');
      }
    }
  }

  /// Add a point to location history
  void _addToHistory(LatLng point) {
    _historyPoints.add(point);

    // Limit history size
    if (_historyPoints.length > widget.maxHistoryItems) {
      _historyPoints.removeAt(0);
    }
  }

  /// Clear location history
  void _clearHistory() {
    setState(() {
      _historyPoints.clear();
    });
  }

  /// Calculate distance between two points
  double _calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude
  ) {
    // Calculate distance in meters
    double distanceInMeters = Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude
    );

    // Convert to selected unit
    double convertedDistance;
    switch (widget.distanceUnit) {
      case DistanceUnit.meters:
        convertedDistance = distanceInMeters;
        break;
      case DistanceUnit.kilometers:
        convertedDistance = distanceInMeters / 1000;
        break;
      case DistanceUnit.miles:
        convertedDistance = distanceInMeters / 1609.34;
        break;
      case DistanceUnit.feet:
        convertedDistance = distanceInMeters * 3.28084;
        break;
      case DistanceUnit.nauticalMiles:
        convertedDistance = distanceInMeters / 1852;
        break;
    }

    // Call callback
    if (widget.onDistanceCalculated != null) {
      widget.onDistanceCalculated!(convertedDistance);
    }

    return convertedDistance;
  }

  /// Check if point is inside geofences
  void _checkGeofences(double latitude, double longitude) {
    // Check circle geofences
    if (widget.geofenceCircles != null) {
      for (final circle in widget.geofenceCircles!) {
        // Calculate distance from point to circle center
        double distance = Geolocator.distanceBetween(
          latitude,
          longitude,
          circle.latitude,
          circle.longitude
        );

        // Check if point is inside circle
        bool isInside = distance <= circle.radius;

        // Get previous status
        GeofenceStatus? previousStatus = _geofenceStatuses[circle.identifier];

        // Determine current status
        GeofenceStatus currentStatus;
        if (isInside) {
          currentStatus = previousStatus == GeofenceStatus.enter
              ? GeofenceStatus.dwell
              : GeofenceStatus.enter;
        } else {
          currentStatus = GeofenceStatus.exit;
        }

        // Update status if changed
        if (previousStatus != currentStatus) {
          _geofenceStatuses[circle.identifier] = currentStatus;

          // Call callback
          if (widget.onGeofenceStatusChanged != null && widget.showGeofenceAlerts) {
            widget.onGeofenceStatusChanged!(currentStatus, circle.identifier);
          }
        }
      }
    }

    // Check polygon geofences
    if (widget.geofencePolygons != null) {
      for (final polygon in widget.geofencePolygons!) {
        // Check if point is inside polygon
        bool isInside = _isPointInPolygon(LatLng(latitude, longitude), polygon.points);

        // Get previous status
        GeofenceStatus? previousStatus = _geofenceStatuses[polygon.identifier];

        // Determine current status
        GeofenceStatus currentStatus;
        if (isInside) {
          currentStatus = previousStatus == GeofenceStatus.enter
              ? GeofenceStatus.dwell
              : GeofenceStatus.enter;
        } else {
          currentStatus = GeofenceStatus.exit;
        }

        // Update status if changed
        if (previousStatus != currentStatus) {
          _geofenceStatuses[polygon.identifier] = currentStatus;

          // Call callback
          if (widget.onGeofenceStatusChanged != null && widget.showGeofenceAlerts) {
            widget.onGeofenceStatusChanged!(currentStatus, polygon.identifier);
          }
        }
      }
    }
  }

  /// Check if a point is inside a polygon
  bool _isPointInPolygon(LatLng point, List<LatLng> polygon) {
    // Implementation of ray casting algorithm
    bool isInside = false;

    for (int i = 0, j = polygon.length - 1; i < polygon.length; i++) {
      bool intersect = ((polygon[i].latitude > point.latitude) != (polygon[j].latitude > point.latitude)) &&
          (point.longitude < (polygon[j].longitude - polygon[i].longitude) *
           (point.latitude - polygon[i].latitude) /
           (polygon[j].latitude - polygon[i].latitude) + polygon[i].longitude);

      if (intersect) {
        isInside = !isInside;
      }

      j = i;
    }

    return isInside;
  }

  /// Handle map tap
  void _onMapTap(LatLng position) {
    if (widget.isReadOnly || widget.isDisabled) return;

    setState(() {
      _latitude = position.latitude;
      _longitude = position.longitude;
      _markers = {
        Marker(
          markerId: const MarkerId('tapped_location'),
          position: position,
        ),
      };
    });

    _getAddressFromCoordinates(position.latitude, position.longitude);

    if (widget.onLocationChanged != null) {
      widget.onLocationChanged!(position.latitude, position.longitude);
    }
  }

  /// Clear all data
  void _clearData() {
    setState(() {
      _latitude = null;
      _longitude = null;
      _address = null;
      _accuracy = null;
      _altitude = null;
      _speed = null;
      _heading = null;
      _timestamp = null;
      _markers = {};
      _historyPoints = [];
      _addressController.clear();
      _errorMessage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Main container
    return Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: widget.elevation * 2,
                  spreadRadius: widget.elevation / 2,
                  offset: Offset(0, widget.elevation),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Label
          if (widget.label != null) ...[
            Row(
              children: [
                if (widget.showIcon && widget.icon != null)
                  Icon(
                    widget.icon,
                    color: widget.textColor,
                    size: widget.fontSize * 1.2,
                  ),
                if (widget.showIcon && widget.icon != null)
                  const SizedBox(width: 4),
                Text(
                  widget.label!,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize,
                    fontWeight: widget.fontWeight,
                  ),
                ),
                if (widget.isRequired)
                  Text(
                    ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Main content based on display mode
          _buildMainContent(),

          // Helper text
          if (widget.helperText != null && _errorMessage == null) ...[
            const SizedBox(height: 4),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: widget.textColor.withOpacity(0.6),
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ],

          // Error text
          if (_errorMessage != null || widget.errorText != null) ...[
            const SizedBox(height: 4),
            Text(
              _errorMessage ?? widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build the main content based on display mode
  Widget _buildMainContent() {
    switch (widget.displayMode) {
      case GeolocationDisplayMode.mapAndAddress:
        return _buildMapAndAddressView();
      case GeolocationDisplayMode.mapOnly:
        return _buildMapView();
      case GeolocationDisplayMode.addressOnly:
        return _buildAddressView();
      case GeolocationDisplayMode.coordinatesOnly:
        return _buildCoordinatesView();
      case GeolocationDisplayMode.compact:
        return _buildCompactView();
      case GeolocationDisplayMode.detailed:
        return _buildDetailedView();
    }
  }

  /// Build map and address view
  Widget _buildMapAndAddressView() {
    return Column(
      children: [
        // Map
        _buildMapView(),
        const SizedBox(height: 8),
        // Address field
        _buildAddressField(),
        // Buttons
        _buildButtonRow(),
        // Additional info
        if (widget.showCoordinates) _buildCoordinatesView(),
        if (widget.showDistance && widget.enableDistanceCalculation) _buildDistanceView(),
      ],
    );
  }

  /// Build map view
  Widget _buildMapView() {
    return Container(
      height: widget.isCompact ? 150 : 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(color: widget.borderColor.withOpacity(0.5)),
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          // Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _latitude != null && _longitude != null
                  ? LatLng(_latitude!, _longitude!)
                  : const LatLng(0, 0),
              zoom: widget.mapZoom,
            ),
            markers: _markers,
            mapType: widget.mapType,
            zoomControlsEnabled: widget.showMapControls,
            zoomGesturesEnabled: widget.enableMapGestures,
            scrollGesturesEnabled: widget.enableMapGestures,
            rotateGesturesEnabled: widget.enableMapGestures,
            tiltGesturesEnabled: widget.enableMapGestures,
            compassEnabled: widget.showMapCompass,
            mapToolbarEnabled: widget.showMapToolbar,
            trafficEnabled: widget.showTraffic,
            buildingsEnabled: widget.showBuildings,
            myLocationEnabled: widget.showUserLocation,
            myLocationButtonEnabled: false, // We'll use our own button
            onMapCreated: (controller) {
              _mapController = controller;
            },
            onTap: widget.isReadOnly || widget.isDisabled ? null : _onMapTap,
            polylines: widget.showHistoryPath && _historyPoints.isNotEmpty
                ? {
                    Polyline(
                      polylineId: const PolylineId('history_path'),
                      points: _historyPoints,
                      color: widget.historyPathColor,
                      width: widget.historyPathWidth.toInt(),
                    ),
                  }
                : {},
            circles: widget.geofenceCircles != null
                ? widget.geofenceCircles!.map((circle) {
                    return Circle(
                      circleId: CircleId(circle.identifier),
                      center: LatLng(circle.latitude, circle.longitude),
                      radius: circle.radius,
                      fillColor: circle.color.withOpacity(0.2),
                      strokeColor: circle.color,
                      strokeWidth: 2,
                    );
                  }).toSet()
                : {},
            polygons: widget.geofencePolygons != null
                ? widget.geofencePolygons!.map((polygon) {
                    return Polygon(
                      polygonId: PolygonId(polygon.identifier),
                      points: polygon.points,
                      fillColor: polygon.color.withOpacity(0.2),
                      strokeColor: polygon.color,
                      strokeWidth: 2,
                    );
                  }).toSet()
                : {},
          ),

          // Loading indicator
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.1),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Map controls
          if (widget.showMapControls)
            Positioned(
              right: 8,
              bottom: 8,
              child: Column(
                children: [
                  // Zoom in
                  FloatingActionButton.small(
                    heroTag: 'zoom_in',
                    onPressed: () {
                      _mapController?.animateCamera(CameraUpdate.zoomIn());
                    },
                    backgroundColor: widget.buttonColor,
                    foregroundColor: widget.buttonTextColor,
                    child: const Icon(Icons.add),
                  ),
                  const SizedBox(height: 4),
                  // Zoom out
                  FloatingActionButton.small(
                    heroTag: 'zoom_out',
                    onPressed: () {
                      _mapController?.animateCamera(CameraUpdate.zoomOut());
                    },
                    backgroundColor: widget.buttonColor,
                    foregroundColor: widget.buttonTextColor,
                    child: const Icon(Icons.remove),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build address field
  Widget _buildAddressField() {
    return TextField(
      controller: _addressController,
      enabled: !widget.isDisabled && !widget.isReadOnly && widget.enableAddressSearch,
      decoration: InputDecoration(
        hintText: widget.addressHint,
        suffixIcon: widget.enableAddressSearch
            ? IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  if (_addressController.text.isNotEmpty) {
                    _getCoordinatesFromAddress(_addressController.text);
                  }
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        color: widget.textColor,
        fontSize: widget.fontSize,
      ),
      textAlign: widget.textAlign,
      onSubmitted: widget.enableAddressSearch ? _getCoordinatesFromAddress : null,
    );
  }

  /// Build button row
  Widget _buildButtonRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Current location button
          if (widget.showCurrentLocationButton)
            ElevatedButton.icon(
              onPressed: widget.isDisabled || widget.isReadOnly ? null : _getCurrentLocation,
              icon: const Icon(Icons.my_location),
              label: Text(widget.currentLocationButtonText),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
                foregroundColor: widget.buttonTextColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            ),
          const Spacer(),
          // Clear button
          if (widget.showClearButton)
            TextButton.icon(
              onPressed: widget.isDisabled || widget.isReadOnly ? null : _clearData,
              icon: const Icon(Icons.clear),
              label: Text(widget.clearButtonText),
            ),
        ],
      ),
    );
  }

  /// Build coordinates view
  Widget _buildCoordinatesView() {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Coordinates:',
            style: TextStyle(
              color: widget.textColor,
              fontSize: widget.fontSize * 0.9,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Latitude: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                _latitude != null ? _latitude!.toStringAsFixed(6) : 'N/A',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                'Longitude: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                _longitude != null ? _longitude!.toStringAsFixed(6) : 'N/A',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (widget.showLocationAccuracy && _accuracy != null)
            Row(
              children: [
                Text(
                  'Accuracy: ',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize * 0.9,
                  ),
                ),
                Text(
                  '${_accuracy!.toStringAsFixed(1)} m',
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize * 0.9,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// Build distance view
  Widget _buildDistanceView() {
    if (widget.targetLatitude == null || widget.targetLongitude == null || _latitude == null || _longitude == null) {
      return const SizedBox.shrink();
    }

    final distance = _calculateDistance(
      _latitude!,
      _longitude!,
      widget.targetLatitude!,
      widget.targetLongitude!,
    );

    String distanceText;
    String unit;

    switch (widget.distanceUnit) {
      case DistanceUnit.meters:
        distanceText = distance.toStringAsFixed(0);
        unit = 'm';
        break;
      case DistanceUnit.kilometers:
        distanceText = distance.toStringAsFixed(2);
        unit = 'km';
        break;
      case DistanceUnit.miles:
        distanceText = distance.toStringAsFixed(2);
        unit = 'mi';
        break;
      case DistanceUnit.feet:
        distanceText = distance.toStringAsFixed(0);
        unit = 'ft';
        break;
      case DistanceUnit.nauticalMiles:
        distanceText = distance.toStringAsFixed(2);
        unit = 'nm';
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          Text(
            'Distance: ',
            style: TextStyle(
              color: widget.textColor,
              fontSize: widget.fontSize * 0.9,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            distanceText,
            style: TextStyle(
              color: widget.textColor,
              fontSize: widget.fontSize * 0.9,
            ),
          ),
          if (widget.showDistanceUnit)
            Text(
              ' $unit',
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize * 0.9,
              ),
            ),
        ],
      ),
    );
  }

  /// Build address view
  Widget _buildAddressView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAddressField(),
        _buildButtonRow(),
      ],
    );
  }

  /// Build compact view
  Widget _buildCompactView() {
    return Row(
      children: [
        // Small map
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border: Border.all(color: widget.borderColor.withOpacity(0.5)),
          ),
          clipBehavior: Clip.antiAlias,
          child: GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _latitude != null && _longitude != null
                  ? LatLng(_latitude!, _longitude!)
                  : const LatLng(0, 0),
              zoom: widget.mapZoom,
            ),
            markers: _markers,
            mapType: widget.mapType,
            zoomControlsEnabled: false,
            zoomGesturesEnabled: false,
            scrollGesturesEnabled: false,
            rotateGesturesEnabled: false,
            tiltGesturesEnabled: false,
            compassEnabled: false,
            mapToolbarEnabled: false,
            myLocationEnabled: widget.showUserLocation,
            myLocationButtonEnabled: false,
            onMapCreated: (controller) {
              _mapController = controller;
            },
          ),
        ),
        const SizedBox(width: 8),
        // Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_address != null)
                Text(
                  _address!,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: widget.fontSize * 0.9,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              if (_latitude != null && _longitude != null)
                Text(
                  '${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                  style: TextStyle(
                    color: widget.textColor.withOpacity(0.7),
                    fontSize: widget.fontSize * 0.8,
                  ),
                ),
              const SizedBox(height: 4),
              // Current location button
              if (widget.showCurrentLocationButton)
                TextButton.icon(
                  onPressed: widget.isDisabled || widget.isReadOnly ? null : _getCurrentLocation,
                  icon: const Icon(Icons.my_location, size: 16),
                  label: Text(
                    widget.currentLocationButtonText,
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build detailed view
  Widget _buildDetailedView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Map
        _buildMapView(),
        const SizedBox(height: 8),
        // Address field
        _buildAddressField(),
        // Buttons
        _buildButtonRow(),
        // Coordinates
        _buildCoordinatesView(),
        // Additional data
        if (widget.showAltitude && _altitude != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Altitude: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                '${_altitude!.toStringAsFixed(1)} m',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
        if (widget.showSpeed && _speed != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Speed: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                '${_speed!.toStringAsFixed(1)} m/s',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
        if (widget.showHeading && _heading != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Heading: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                '${_heading!.toStringAsFixed(1)}°',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
        if (widget.showTimestamp && _timestamp != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                'Time: ',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
              Text(
                '${_timestamp!.hour.toString().padLeft(2, '0')}:${_timestamp!.minute.toString().padLeft(2, '0')}:${_timestamp!.second.toString().padLeft(2, '0')}',
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
        // Distance
        if (widget.showDistance && widget.enableDistanceCalculation)
          _buildDistanceView(),
      ],
    );
  }
}