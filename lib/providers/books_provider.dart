import '../models/books/books_model.dart';
import '../services/books_service.dart';
import 'base_provider.dart';

/// Provider for managing books state and operations
class BooksProvider extends BaseProvider {
  final BooksService _booksService = BooksService();

  List<BooksModel> _books = [];
  List<BooksModel> _filteredBooks = [];
  String _searchQuery = '';

  /// Get all books
  List<BooksModel> get books => _books;

  /// Get filtered books based on search query
  List<BooksModel> get filteredBooks => _filteredBooks;

  /// Get current search query
  String get searchQuery => _searchQuery;

  /// Get books count
  int get booksCount => _books.length;

  /// Get filtered books count
  int get filteredBooksCount => _filteredBooks.length;

  /// Fetch all books from the API
  Future<void> fetchBooks({
    String? tenantId,
    String? authToken,
  }) async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        List<BooksModel> fetchedBooks;

        if (tenantId != null && authToken != null) {
          fetchedBooks = await _booksService.getAllBooks(
            tenantId: tenantId,
            authToken: authToken,
          );
        } else {
          // Use default values if not provided
          fetchedBooks = await _booksService.getAllBooksWithDefaults();
        }

        _books = fetchedBooks;
        _applySearchFilter();
      },
      context: 'Fetching books',
    );
  }

  /// Search books by name
  void searchBooks(String query) {
    _searchQuery = query.toLowerCase().trim();
    _applySearchFilter();
    notifyListeners();
  }

  /// Clear search and show all books
  void clearSearch() {
    _searchQuery = '';
    _applySearchFilter();
    notifyListeners();
  }

  /// Apply search filter to books
  void _applySearchFilter() {
    if (_searchQuery.isEmpty) {
      _filteredBooks = List.from(_books);
    } else {
      _filteredBooks = _books.where((book) {
        final bookName = book.bookName?.toLowerCase() ?? '';
        final bookId = book.bookId?.toLowerCase() ?? '';
        return bookName.contains(_searchQuery) || bookId.contains(_searchQuery);
      }).toList();
    }
  }

  /// Get book by ID
  BooksModel? getBookById(String bookId) {
    try {
      return _books.firstWhere((book) => book.bookId == bookId);
    } catch (e) {
      return null;
    }
  }

  /// Get books with objectives count greater than specified value
  List<BooksModel> getBooksWithObjectivesGreaterThan(int count) {
    return _books.where((book) => (book.objectiveCount ?? 0) > count).toList();
  }

  /// Sort books by name (ascending or descending)
  void sortBooksByName({bool ascending = true}) {
    _books.sort((a, b) {
      final nameA = a.bookName ?? '';
      final nameB = b.bookName ?? '';
      return ascending ? nameA.compareTo(nameB) : nameB.compareTo(nameA);
    });
    _applySearchFilter();
    notifyListeners();
  }

  /// Sort books by objective count (ascending or descending)
  void sortBooksByObjectiveCount({bool ascending = true}) {
    _books.sort((a, b) {
      final countA = a.objectiveCount ?? 0;
      final countB = b.objectiveCount ?? 0;
      return ascending ? countA.compareTo(countB) : countB.compareTo(countA);
    });
    _applySearchFilter();
    notifyListeners();
  }

  /// Refresh books data
  Future<void> refreshBooks({
    String? tenantId,
    String? authToken,
  }) async {
    await fetchBooks(tenantId: tenantId, authToken: authToken);
  }

  /// Clear all books data
  void clearBooks() {
    _books.clear();
    _filteredBooks.clear();
    _searchQuery = '';
    notifyListeners();
  }
}
