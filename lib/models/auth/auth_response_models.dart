import '../base_model.dart';
import '../user.dart';
import 'login_model.dart';
import 'logout_model.dart';

/// Error details model for API responses
class ErrorDetails extends BaseModel {
  final String error;
  final String message;
  final Map<String, dynamic>? details;

  ErrorDetails({
    required this.error,
    required this.message,
    this.details,
  });

  factory ErrorDetails.fromJson(Map<String, dynamic> json) {
    return ErrorDetails(
      error: json['error'] ?? '',
      message: json['message'] ?? '',
      details: json['details'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'message': message,
      'details': details,
    };
  }

  @override
  ErrorDetails copyWith({
    String? error,
    String? message,
    Map<String, dynamic>? details,
  }) {
    return ErrorDetails(
      error: error ?? this.error,
      message: message ?? this.message,
      details: details ?? this.details,
    );
  }
}

/// Response data model for login and registration operations
class AuthData extends BaseModel {
  /// The authenticated user (optional, null in case of error)
  final User? user;

  /// The authentication token (optional, null in case of error)
  final String? token;

  /// The refresh token (optional)
  final String? refreshToken;

  /// Token type (optional)
  final String? tokenType;

  /// Token expiration time (optional)
  final int? expiresAt;

  /// Error details (optional, present in case of error)
  final ErrorDetails? error;

  AuthData({
    this.user,
    this.token,
    this.refreshToken,
    this.tokenType,
    this.expiresAt,
    this.error,
  });

  /// Factory constructor for successful auth response (like LoginModel)
  factory AuthData.fromLoginModel(LoginModel loginModel) {
    return AuthData(
      user: loginModel.user != null
          ? User.fromUserDetails(loginModel.user!)
          : null,
      token: loginModel.accessToken,
      refreshToken: loginModel.refreshToken,
      tokenType: loginModel.tokenType,
      expiresAt: loginModel.expiresAt,
    );
  }

  /// Factory constructor for error response
  factory AuthData.fromError(Map<String, dynamic> errorJson) {
    final detail = errorJson['detail'];
    return AuthData(
      error: detail != null ? ErrorDetails.fromJson(detail) : null,
    );
  }

  factory AuthData.fromJson(Map<String, dynamic> json) {
    // Check if this is an error response
    if (json.containsKey('detail')) {
      return AuthData.fromError(json);
    }

    // Handle success response
    return AuthData(
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      token: json['token'] ?? json['access_token'],
      refreshToken: json['refreshToken'] ?? json['refresh_token'],
      tokenType: json['tokenType'] ?? json['token_type'],
      expiresAt: json['expiresAt'] ?? json['expires_at'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'user': user?.toJson(),
      'token': token,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresAt': expiresAt,
      'error': error?.toJson(),
    };
  }

  @override
  AuthData copyWith({
    User? user,
    String? token,
    String? refreshToken,
    String? tokenType,
    int? expiresAt,
    ErrorDetails? error,
  }) {
    return AuthData(
      user: user ?? this.user,
      token: token ?? this.token,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresAt: expiresAt ?? this.expiresAt,
      error: error ?? this.error,
    );
  }

  /// Check if this is an error response
  bool get hasError => error != null;

  /// Check if this is a successful response
  bool get isSuccess => !hasError && token != null;
}

/// Response data model for saved auth data operations
class SavedAuthData extends BaseModel {
  /// Whether the user is logged in
  final bool isLoggedIn;

  /// The saved user (optional)
  final User? user;

  /// The saved token (optional)
  final String? token;

  /// Whether remember me is enabled
  final bool rememberMe;

  /// The saved email (optional)
  final String? email;

  /// The saved password (optional)
  final String? password;

  /// The saved mobile number (optional)
  final String? mobile;

  /// The saved access token (optional)
  final String? accessToken;

  /// The saved refresh token (optional)
  final String? refreshToken;

  SavedAuthData({
    required this.isLoggedIn,
    this.user,
    this.token,
    required this.rememberMe,
    this.email,
    this.password,
    this.mobile,
    this.accessToken,
    this.refreshToken,
  });

  factory SavedAuthData.fromJson(Map<String, dynamic> json) {
    return SavedAuthData(
      isLoggedIn: json['isLoggedIn'] ?? false,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      token: json['token'],
      rememberMe: json['rememberMe'] ?? false,
      email: json['email'],
      password: json['password'],
      mobile: json['mobile'],
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'isLoggedIn': isLoggedIn,
      'user': user?.toJson(),
      'token': token,
      'rememberMe': rememberMe,
      'email': email,
      'password': password,
      'mobile': mobile,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
    };
  }

  @override
  SavedAuthData copyWith({
    bool? isLoggedIn,
    User? user,
    String? token,
    bool? rememberMe,
    String? email,
    String? password,
    String? mobile,
    String? accessToken,
    String? refreshToken,
  }) {
    return SavedAuthData(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      user: user ?? this.user,
      token: token ?? this.token,
      rememberMe: rememberMe ?? this.rememberMe,
      email: email ?? this.email,
      password: password ?? this.password,
      mobile: mobile ?? this.mobile,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }
}

/// Response data model for user profile operations
class UserProfileData extends BaseModel {
  /// The user profile information
  final User user;

  /// Raw user data from the API (optional)
  final Map<String, dynamic>? userData;

  UserProfileData({
    required this.user,
    this.userData,
  });

  factory UserProfileData.fromJson(Map<String, dynamic> json) {
    return UserProfileData(
      user: User.fromJson(json['user']),
      userData: json['userData'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'userData': userData,
    };
  }

  @override
  UserProfileData copyWith({
    User? user,
    Map<String, dynamic>? userData,
  }) {
    return UserProfileData(
      user: user ?? this.user,
      userData: userData ?? this.userData,
    );
  }
}

/// Response data model for logout operations
class LogoutData extends BaseModel {
  /// Whether the logout was successful
  final bool success;

  /// The logout message
  final String? message;

  /// Additional data from logout response
  final Map<String, dynamic>? data;

  LogoutData({
    required this.success,
    this.message,
    this.data,
  });

  /// Factory constructor from LogoutModel
  factory LogoutData.fromLogoutModel(LogoutModel logoutModel) {
    return LogoutData(
      success: logoutModel.success ?? false,
      message: logoutModel.message,
      data: logoutModel.data?.toJson(),
    );
  }

  factory LogoutData.fromJson(Map<String, dynamic> json) {
    return LogoutData(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
    };
  }

  @override
  LogoutData copyWith({
    bool? success,
    String? message,
    Map<String, dynamic>? data,
  }) {
    return LogoutData(
      success: success ?? this.success,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}
