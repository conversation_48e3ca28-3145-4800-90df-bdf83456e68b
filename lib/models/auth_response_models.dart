import 'base_model.dart';
import 'user.dart';

/// Response data model for login and registration operations
class AuthData extends BaseModel {
  /// The authenticated user
  final User user;

  /// The authentication token
  final String token;

  /// The refresh token (optional)
  final String? refreshToken;

  /// Token expiration time (optional)
  final String? expiresAt;

  AuthData({
    required this.user,
    required this.token,
    this.refreshToken,
    this.expiresAt,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      user: User.fromJson(json['user']),
      token: json['token'],
      refreshToken: json['refreshToken'],
      expiresAt: json['expiresAt'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'token': token,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt,
    };
  }

  @override
  AuthData copyWith({
    User? user,
    String? token,
    String? refreshToken,
    String? expiresAt,
  }) {
    return AuthData(
      user: user ?? this.user,
      token: token ?? this.token,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }
}

/// Response data model for saved auth data operations
class SavedAuthData extends BaseModel {
  /// Whether the user is logged in
  final bool isLoggedIn;

  /// The saved user (optional)
  final User? user;

  /// The saved token (optional)
  final String? token;

  /// Whether remember me is enabled
  final bool rememberMe;

  /// The saved email (optional)
  final String? email;

  /// The saved password (optional)
  final String? password;

  /// The saved mobile number (optional)
  final String? mobile;

  /// The saved access token (optional)
  final String? accessToken;

  /// The saved refresh token (optional)
  final String? refreshToken;

  SavedAuthData({
    required this.isLoggedIn,
    this.user,
    this.token,
    required this.rememberMe,
    this.email,
    this.password,
    this.mobile,
    this.accessToken,
    this.refreshToken,
  });

  factory SavedAuthData.fromJson(Map<String, dynamic> json) {
    return SavedAuthData(
      isLoggedIn: json['isLoggedIn'] ?? false,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      token: json['token'],
      rememberMe: json['rememberMe'] ?? false,
      email: json['email'],
      password: json['password'],
      mobile: json['mobile'],
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'isLoggedIn': isLoggedIn,
      'user': user?.toJson(),
      'token': token,
      'rememberMe': rememberMe,
      'email': email,
      'password': password,
      'mobile': mobile,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
    };
  }

  @override
  SavedAuthData copyWith({
    bool? isLoggedIn,
    User? user,
    String? token,
    bool? rememberMe,
    String? email,
    String? password,
    String? mobile,
    String? accessToken,
    String? refreshToken,
  }) {
    return SavedAuthData(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      user: user ?? this.user,
      token: token ?? this.token,
      rememberMe: rememberMe ?? this.rememberMe,
      email: email ?? this.email,
      password: password ?? this.password,
      mobile: mobile ?? this.mobile,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }
}

/// Response data model for user profile operations
class UserProfileData extends BaseModel {
  /// The user profile information
  final User user;

  /// Raw user data from the API (optional)
  final Map<String, dynamic>? userData;

  UserProfileData({
    required this.user,
    this.userData,
  });

  factory UserProfileData.fromJson(Map<String, dynamic> json) {
    return UserProfileData(
      user: User.fromJson(json['user']),
      userData: json['userData'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'userData': userData,
    };
  }

  @override
  UserProfileData copyWith({
    User? user,
    Map<String, dynamic>? userData,
  }) {
    return UserProfileData(
      user: user ?? this.user,
      userData: userData ?? this.userData,
    );
  }
}
