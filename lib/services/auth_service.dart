import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:nsl/models/login_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../utils/logger.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../config/environment.dart';
import 'base_api_service.dart';

class AuthService extends BaseApiService {
  // Use constants for storing auth data
  static String get _tokenKey => AppConstants.tokenKey;
  static String get _userKey => AppConstants.userKey;
  static String get _rememberMeKey => AppConstants.rememberMeKey;
  static String get _emailKey => AppConstants.emailKey;
  static String get _passwordKey => AppConstants.passwordKey;
  static String get _mobileKey => AppConstants.mobileKey;

  // Login using the API
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      Logger.info('Attempting login for user: $email');
      LoginModel? loginModel;

      // Validate email
      // final emailError = Validators.validateEmail(email);
      // if (emailError != null) {
      //   return {
      //     'success': false,
      //     'message': emailError,
      //   };
      // }

      // Validate password
      final passwordError = Validators.validatePassword(password);
      if (passwordError != null) {
        return {
          'success': false,
          'message': passwordError,
        };
      }

      // Prepare the request payload as form data
      final formData = {
        'username': email, // Using email as username
        'password': password,
      };

      // Make the API call with form-urlencoded content type
      final response = await dio.post(
        Environment.loginUrl,
        data: formData,
        // options: Options(
        //   contentType: Headers.formUrlEncodedContentType,
        //   headers: {
        //     'Content-Type': 'application/x-www-form-urlencoded',
        //   },
        // ),
      );

      Logger.info('Login API response status: ${response.statusCode}');
      Logger.info('Login API response data: ${response.data}');

      // Check if the login was successful
      if (response.statusCode == 200) {
        // Extract tokens from the response
        loginModel = LoginModel.fromJson(response.data);
        final accessToken = loginModel.accessToken ?? '';
        final refreshToken = loginModel.refreshToken ?? '';

        // Get user details from the JWT token
        // final Map<String, dynamic> userDetails = _decodeJwtToken(accessToken);

        // Create a user object with the token and user details
        final user = User(
          id: loginModel.user?.userId ?? '',
          email: loginModel.user?.email ?? email,
          name: loginModel.user?.firstName ?? email,
          username: loginModel.user?.username ?? email,
          accessToken: accessToken,
          refreshToken: refreshToken,
          roles: loginModel.user?.roles,
          tenantId: loginModel.user?.tenantId,
        );

        return {
          'success': true,
          'user': user,
          'token': accessToken,
        };
      } else {
        // Login failed
        final errorMessage =
            response.data['detail']['message'] ?? 'Login failed';
        Logger.error('Login failed: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Login error: $e');
      String errorMessage = 'An error occurred during login';

      // Try to extract more specific error message if available
      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage = 'Server is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage = 'Login service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        }
      }

      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }

  // Registration using the API
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String username,
    required String role,
    required String organization,
    String? profilePicture,
  }) async {
    try {
      Logger.info('Attempting registration for user: $email');

      // Validate name
      final nameError = Validators.validateRequired(name, 'Name');
      if (nameError != null) {
        return {
          'success': false,
          'message': nameError,
        };
      }

      // Validate username
      final usernameError = Validators.validateUsername(username);
      if (usernameError != null) {
        return {
          'success': false,
          'message': usernameError,
        };
      }

      // Validate email
      final emailError = Validators.validateEmail(email);
      if (emailError != null) {
        return {
          'success': false,
          'message': emailError,
        };
      }

      // // Validate mobile
      // final mobileError = Validators.validateMobile(mobile);
      // if (mobileError != null) {
      //   return {
      //     'success': false,
      //     'message': mobileError,
      //   };
      // }

      // Validate role
      final roleError = Validators.validateRole(role);
      if (roleError != null) {
        return {
          'success': false,
          'message': roleError,
        };
      }

      // Validate organization
      final organizationError = Validators.validateOrganization(organization);
      if (organizationError != null) {
        return {
          'success': false,
          'message': organizationError,
        };
      }

      // Validate password
      final passwordError = Validators.validatePassword(password);
      if (passwordError != null) {
        return {
          'success': false,
          'message': passwordError,
        };
      }

      // Split the name into first and last name
      List<String> nameParts = name.trim().split(' ');
      String firstName = nameParts.first;
      String lastName =
          nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // Prepare the request payload
      final payload = {
        'username': username,
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'roles': [role],
        'org_unit_id': organization,
        // 'org_unit_id': "org_it",
        'tenant_id': 't001',
      };

      Logger.info('Registration payload: $payload');

      // Make the API call
      final response = await dio.post(
        Environment.registerUrl,
        data: jsonEncode(payload),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Registration API response status: ${response.statusCode}');
      Logger.info('Registration API response data: ${response.data}');

      // Check if the registration was successful
      if (response.statusCode == 200) {
        // Create a user object directly from the response
        final user = User.fromAuth0Registration(response.data);

        // For now, we'll use a mock token since the API doesn't return one
        final token = 'mock-token-${DateTime.now().millisecondsSinceEpoch}';

        return {
          'success': true,
          'user': user,
          'token': token,
          'message': response.data['message'],
        };
      } else {
        // Registration failed
        final errorMessage = response.data['detail'] ?? 'Registration failed';
        Logger.error('Registration failed: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Registration error: $e');
      return {
        'success': false,
        'message': 'An error occurred during registration',
      };
    }
  }

  // Save auth data to shared preferences
  Future<void> saveAuthData(
      User user, String token, bool rememberMe, String email, String password,
      {String? mobile}) async {
    Logger.info('Saving auth data to SharedPreferences');
    Logger.info('Remember Me: $rememberMe');
    final prefs = await SharedPreferences.getInstance();

    // Always save the token and user data for the current session
    await prefs.setString(_tokenKey, token);
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
    await prefs.setBool('isLoggedIn', true);

    // Save access token and refresh token if available
    if (user.accessToken != null) {
      await prefs.setString('access_token', user.accessToken!);
    }
    if (user.refreshToken != null) {
      await prefs.setString('refresh_token', user.refreshToken!);
    }

    // Only save credentials if remember me is checked
    if (rememberMe) {
      Logger.info('Remember Me is checked, saving credentials');
      await prefs.setString(_emailKey, email);
      await prefs.setString(_passwordKey, password);
      if (mobile != null) {
        await prefs.setString(_mobileKey, mobile);
        Logger.info('Mobile number saved: $mobile');
      }
      Logger.info('Credentials saved for: $email');
    } else {
      // Clear saved credentials if remember me is unchecked
      Logger.info('Remember Me is unchecked, clearing credentials');
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
      await prefs.remove(_mobileKey);
    }

    // Always save the remember me preference
    await prefs.setBool(_rememberMeKey, rememberMe);
    Logger.info('Remember Me preference saved: $rememberMe');

    // Verify the data was saved correctly
    final savedRememberMe = prefs.getBool(_rememberMeKey);
    final savedEmail = prefs.getString(_emailKey);
    Logger.info(
        'Verification - Remember Me: $savedRememberMe, Email: $savedEmail');

    Logger.info('Auth data saved for user: ${user.email}');
  }

  // Get saved auth data
  Future<Map<String, dynamic>> getSavedAuthData() async {
    Logger.info('Getting saved auth data from SharedPreferences');
    final prefs = await SharedPreferences.getInstance();

    // Log all keys in SharedPreferences for debugging
    final keys = prefs.getKeys();
    Logger.info('All keys in SharedPreferences: $keys');

    final token = prefs.getString(_tokenKey);
    final userJson = prefs.getString(_userKey);
    final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
    final email = prefs.getString(_emailKey);
    final password = prefs.getString(_passwordKey);
    final mobile = prefs.getString(_mobileKey);
    final accessToken = prefs.getString('access_token');
    final refreshToken = prefs.getString('refresh_token');
    final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

    Logger.info('Retrieved from SharedPreferences:');
    Logger.info('- Remember Me: $rememberMe');
    Logger.info('- Email: $email');
    Logger.info('- Password: ${password != null ? '******' : 'null'}');
    Logger.info('- Mobile: $mobile');
    Logger.info('- Token exists: ${token != null}');
    Logger.info('- Access Token exists: ${accessToken != null}');
    Logger.info('- Refresh Token exists: ${refreshToken != null}');
    Logger.info('- Is Logged In: $isLoggedIn');
    Logger.info('- User JSON exists: ${userJson != null}');

    if ((token != null || accessToken != null) && userJson != null) {
      try {
        User user = User.fromJson(jsonDecode(userJson));

        // If we have access token but it's not in the user object, update it
        if (user.accessToken == null && accessToken != null) {
          user = user.copyWith(
              accessToken: accessToken, refreshToken: refreshToken);
        }

        return {
          'isLoggedIn':
              isLoggedIn || true, // Use stored isLoggedIn or default to true
          'user': user,
          'token':
              token ?? accessToken, // Use token or fall back to accessToken
          'rememberMe': rememberMe,
          'email': email,
          'password': password,
          'mobile': mobile,
          'accessToken': accessToken,
          'refreshToken': refreshToken,
        };
      } catch (e) {
        Logger.error('Error parsing saved user data: $e');
      }
    }

    return {
      'isLoggedIn': isLoggedIn,
      'rememberMe': rememberMe,
      'email': email,
      'password': password,
      'mobile': mobile,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
    };
  }

  // Logout
  Future<Map<String, dynamic>> logout() async {
    try {
      Logger.info('Attempting to logout user');

      // Get a valid token
      final token = await getValidToken();

      if (token != null) {
        try {
          // Create auth options
          final options = createAuthOptions(token);

          // Make the API call to logout
          final response = await dio.post(
            Environment.logoutUrl,
            options: options,
          );

          Logger.info('Logout API response status: ${response.statusCode}');
          Logger.info('Logout API response data: ${response.data}');
        } catch (e) {
          // Even if the API call fails, we still want to clear local data
          Logger.error('Error calling logout API: $e');
        }
      }

      // Get shared preferences
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_rememberMeKey) ?? false;

      // Store email and password temporarily if remember me is checked
      final savedEmail = rememberMe ? prefs.getString(_emailKey) : null;
      final savedPassword = rememberMe ? prefs.getString(_passwordKey) : null;
      final savedMobile = rememberMe ? prefs.getString(_mobileKey) : null;

      // Clear all SharedPreferences values
      await prefs.clear();
      Logger.info('All SharedPreferences values cleared');

      // If remember me is checked, restore email and password
      if (rememberMe) {
        await prefs.setBool(_rememberMeKey, true);
        if (savedEmail != null) {
          await prefs.setString(_emailKey, savedEmail);
        }
        if (savedPassword != null) {
          await prefs.setString(_passwordKey, savedPassword);
        }
        if (savedMobile != null) {
          await prefs.setString(_mobileKey, savedMobile);
        }
        Logger.info('Remember Me is checked, restored credentials');
      }

      Logger.info('User logged out successfully');

      return {
        'success': true,
        'message': 'Logged out successfully',
      };
    } catch (e) {
      Logger.error('Error during logout: $e');
      return {
        'success': false,
        'message': 'An error occurred during logout',
      };
    }
  }

  /// Get current user profile information
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      Logger.info('Fetching user profile information');

      // Get a valid token
      final token = await getValidToken();

      if (token == null) {
        Logger.error('No valid token available to fetch user profile');
        return {
          'success': false,
          'message': 'Authentication required',
        };
      }

      // Create auth options
      final options = createAuthOptions(token);

      // Make the API call
      final response = await dio.get(
        Environment.userProfileUrl,
        options: options,
      );

      Logger.info('User profile API response status: ${response.statusCode}');
      Logger.info('User profile API response data: ${response.data}');

      if (response.statusCode == 200) {
        // Create a user object from the response
        final user = User(
          id: response.data['user_id'] ?? '',
          email: response.data['email'] ?? '',
          name:
              '${response.data['first_name'] ?? ''} ${response.data['last_name'] ?? ''}'
                  .trim(),
          username: response.data['username'] ?? '',
          roles: response.data['roles'] != null
              ? List<String>.from(response.data['roles'])
              : null,
          orgUnits: response.data['org_units'] != null
              ? List<String>.from(response.data['org_units'])
              : null,
          tenantId: response.data['tenant_id'],
          status: response.data['status'],
          disabled: response.data['disabled'],
        );

        // Update the stored user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_userKey, jsonEncode(user.toJson()));

        // Save user ID separately for easy access in API calls
        if (response.data['user_id'] != null) {
          await prefs.setString('user_id', response.data['user_id']);
          Logger.info(
              'User ID saved to SharedPreferences: ${response.data['user_id']}');
        }

        return {
          'success': true,
          'user': user,
          'userData': response.data,
        };
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch user profile';
        Logger.error('Failed to fetch user profile: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Error fetching user profile: $e');
      return {
        'success': false,
        'message': 'An error occurred while fetching user profile',
      };
    }
  }

  // Validation methods moved to Validators utility class

  // Refresh token
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      Logger.info('Attempting to refresh token');

      // Prepare the request payload
      final payload = {
        'refresh_token': refreshToken,
      };

      // Make the API call
      final response = await dio.post(
        Environment.refreshTokenUrl,
        data: jsonEncode(payload),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Refresh token API response status: ${response.statusCode}');
      Logger.info('Refresh token API response data: ${response.data}');

      // Check if the refresh was successful
      if (response.statusCode == 200) {
        // Extract tokens from the response
        final accessToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];
        final expiresAt = response.data['expires_at'];

        // Get user details from the JWT token
        final Map<String, dynamic> userDetails = _decodeJwtToken(accessToken);

        // Create a user object with the token and user details
        final user = User(
          id: userDetails['sub'] ?? '',
          email: userDetails['email'] ?? '',
          name: userDetails['name'] ?? userDetails['username'] ?? '',
          username: userDetails['username'] ?? '',
          accessToken: accessToken,
          refreshToken: newRefreshToken,
          roles: userDetails['roles'] != null
              ? List<String>.from(userDetails['roles'])
              : null,
          orgUnits: userDetails['org_units'] != null
              ? List<String>.from(userDetails['org_units'])
              : null,
          tenantId: userDetails['tenant_id'],
        );

        // Save the new tokens
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('access_token', accessToken);
        await prefs.setString('refresh_token', newRefreshToken);
        await prefs.setString(_userKey, jsonEncode(user.toJson()));

        Logger.info('Token refreshed successfully');

        return {
          'success': true,
          'user': user,
          'token': accessToken,
          'refreshToken': newRefreshToken,
          'expiresAt': expiresAt,
        };
      } else {
        // Refresh failed
        final errorMessage = response.data['message'] ?? 'Token refresh failed';
        Logger.error('Token refresh failed: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Token refresh error: $e');
      return {
        'success': false,
        'message': 'An error occurred during token refresh',
      };
    }
  }

  // Check if token is expired or about to expire
  bool isTokenExpired(String token) {
    try {
      final decodedToken = _decodeJwtToken(token);

      // Get expiration time from token
      final expTime = decodedToken['exp'];
      if (expTime == null) {
        return true; // If no expiration time, consider it expired
      }

      // Convert to DateTime
      final expirationDate =
          DateTime.fromMillisecondsSinceEpoch(expTime * 1000);

      // Check if token is expired or will expire in the next 5 minutes
      final now = DateTime.now();
      final fiveMinutesFromNow = now.add(const Duration(minutes: 5));

      return expirationDate.isBefore(fiveMinutesFromNow);
    } catch (e) {
      Logger.error('Error checking token expiration: $e');
      return true; // Consider expired if there's an error
    }
  }

  // Get a valid token, refreshing if necessary
  @override
  Future<String?> getValidToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      final refreshToken = prefs.getString('refresh_token');

      // If no tokens, return null
      if (accessToken == null || refreshToken == null) {
        return null;
      }

      // Check if token is expired or about to expire
      if (isTokenExpired(accessToken)) {
        Logger.info(
            'Access token is expired or about to expire, refreshing...');

        // Refresh the token
        final refreshResult = await this.refreshToken(refreshToken);

        if (refreshResult['success']) {
          return refreshResult['token'];
        } else {
          // If refresh failed, return null
          return null;
        }
      }

      // Token is still valid
      return accessToken;
    } catch (e) {
      Logger.error('Error getting valid token: $e');
      return null;
    }
  }

  // Get user ID from SharedPreferences
  @override
  Future<String?> getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      Logger.info('Retrieved user ID from SharedPreferences: $userId');
      return userId;
    } catch (e) {
      Logger.error('Error getting user ID from SharedPreferences: $e');
      return null;
    }
  }

  // Decode JWT token to extract user information
  Map<String, dynamic> _decodeJwtToken(String token) {
    try {
      Logger.info('Decoding JWT token');

      // Split the token into parts
      final parts = token.split('.');
      if (parts.length != 3) {
        Logger.error('Invalid JWT token format');
        return {};
      }

      // Decode the payload (second part)
      String payload = parts[1];

      // Add padding if needed
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      // Replace characters that are different in base64Url vs base64
      payload = payload.replaceAll('-', '+').replaceAll('_', '/');

      // Decode the base64
      final decoded = utf8.decode(base64Url.decode(payload));
      final Map<String, dynamic> decodedJson = jsonDecode(decoded);

      Logger.info('JWT token decoded successfully');
      return decodedJson;
    } catch (e) {
      Logger.error('Error decoding JWT token: $e');
      return {};
    }
  }
}
