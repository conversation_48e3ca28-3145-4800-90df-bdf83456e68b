import 'package:dio/dio.dart';
import '../models/books/books_model.dart';
import 'base_api_service.dart';
import '../utils/logger.dart';

/// Service for handling books-related API operations
class BooksService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8000';
  static const String _booksEndpoint = '/api/v2/global_objectives/books/all';

  /// Fetch all books for a specific tenant
  Future<List<BooksModel>> getAllBooks({
    required String tenantId,
    required String authToken,
  }) async {
    try {
      Logger.info('Fetching books for tenant: $tenantId');

      final queryParameters = {
        'tenant_id': tenantId,
      };

      final options = Options(
        headers: {
          'Authorization': 'Bearer $authToken',
        },
      );

      // Override the base URL for this specific request
      final fullUrl = '$_baseUrl$_booksEndpoint';

      final response = await dio.get(
        fullUrl,
        queryParameters: queryParameters,
        options: options,
      );

      Logger.info('Books fetched successfully: ${response.statusCode}');

      if (response.data is List) {
        final List<dynamic> booksJson = response.data;
        return booksJson.map((json) => BooksModel.fromJson(json)).toList();
      } else {
        Logger.error('Unexpected response format: ${response.data}');
        throw Exception('Invalid response format');
      }
    } catch (e) {
      Logger.error('Error fetching books: $e');
      rethrow;
    }
  }

  /// Fetch all books with default tenant and token
  /// This method can be used when tenant and token are stored in preferences
  Future<List<BooksModel>> getAllBooksWithDefaults() async {
    try {
      // You can implement logic here to get tenant_id and token from SharedPreferences
      // For now, using the provided values from the curl command
      const defaultTenantId = 't001';
      const defaultToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMzAiLCJ1c2VybmFtZSI6IktpcmFuMTIzIiwicm9sZXMiOlsiVXNlciJdLCJ0ZW5hbnRfaWQiOiJ0MDAxIiwiZXhwIjoxNzQ4MzM5MDcwfQ.hw8WuZpWOkk-JpREJR0hi75ENhHABfDRsuIsGtpvEck';

      return await getAllBooks(
        tenantId: defaultTenantId,
        authToken: defaultToken,
      );
    } catch (e) {
      Logger.error('Error fetching books with defaults: $e');
      rethrow;
    }
  }
}
