import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

class MyBusinessCollection extends StatefulWidget {
  const MyBusinessCollection({super.key});

  @override
  State<MyBusinessCollection> createState() => _MyBusinessCollectionState();
}

final List<String> bannerImages = [
  'assets/images/my_business/collections/my_business_carousel_one.jpg',
  'assets/images/my_business/collections/my_business_carousel_two.jpg',
  'assets/images/my_business/collections/my_business_carousel_three.jpg',
];
final CarouselSliderController carouselController = CarouselSliderController();
final TextEditingController _searchController = TextEditingController();
int _currentPage = 0;
bool _showSearchBar = false;


class _MyBusinessCollectionState extends State<MyBusinessCollection> {
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: CustomDrawer(),
      body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal:AppSpacing.md),
            child: Column(
                    children: [
            CarouselSlider.builder(
              itemCount: bannerImages.length,
              itemBuilder: (context, index, realIndex) {
                return Image.asset(
                  bannerImages[index],
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height/2,
                      color: Colors.grey[200],
                      child: Center(
                        child: Icon(
                          Icons.broken_image,
                          color: Colors.grey[400],
                          size: 48,
                        ),
                      ),
                    );
                  },
                );
              },
              options: CarouselOptions(
                height: MediaQuery.of(context).size.height / 4.5,
                viewportFraction: 1.0,
                autoPlay: true,
                autoPlayInterval: Duration(seconds: 5),
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentPage = index;
                  });
                },
              ),
              carouselController: carouselController,
            ),
            Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('myBusinessCollections.collections'),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                        _showSearchBar
                            ? SearchBarWidget(
                                controller: _searchController,
                                onClose: _toggleSearchBar,
                              )
                            : GestureDetector(
                                onTap: _toggleSearchBar,
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Container(
                                    margin: const EdgeInsets.only(
                                        right: AppSpacing.sm),
                                    height: 36,
                                    child: HoverableSearchIcon(),
                                  ),
                                ),
                              ),
                      ],
                    ),
                    SizedBox(height: AppSpacing.xs),
                    ],
                  ),
          )),
    );
  }
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search icon
          // Padding(
          //   padding: const EdgeInsets.only(left: 8.0),
          //   child: Icon(Icons.search, size: 18, color: Colors.grey.shade600),
          // ),

          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 14,
                    fontFamily: 'TiemposText'),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_business/search_collection.svg',
        width: 20,
        height: 20,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null, // <-- Change color on hover
      ).toWidget(),
    );
  }
}


