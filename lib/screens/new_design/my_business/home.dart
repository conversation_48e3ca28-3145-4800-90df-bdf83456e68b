import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/global_data.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MyBusinessHome extends StatefulWidget {
  const MyBusinessHome({super.key});

  @override
  State<MyBusinessHome> createState() => _MyBusinessHomeState();
}

final ScrollController _scrollController = ScrollController();
final PageController _labelPageController =
    PageController(viewportFraction: 1.0);
final PageController _pageController = PageController();
final PageController _actionCardController =
    PageController(viewportFraction: 1.0);
final int cardsPerRow = 3;
final int cardsPerPage = 6; // 2 rows × 3 cards each
int currentPage = 0;

class _MyBusinessHomeState extends State<MyBusinessHome> {
  final TextEditingController chatController = TextEditingController();
  final List<Map<String, dynamic>> actionCardsData = [
    {
      'text': 'Procurement Management',
      'icon': 'shopping_cart',
      'image': "assets/images/my_business/solutions/solution_apply.png",
      'action': 'procurement_management',
      'isSelected': true
    },
    {
      'text': 'My Modules and My team',
      'icon': 'people',
      'image':
          "assets/images/my_business/solutions/solution_employee_details.png",
      'action': 'modules_team',
      'isSelected': true
    },
    {
      'text': 'Org Guides and Business Modules',
      'icon': 'business',
      'image': "assets/images/my_business/solutions/solution_my_module.png",
      'action': 'organisation_guides',
      'isSelected': true
    },
    {
      'text': 'Leave management',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_my_attendance.png",
      'action': 'leave_management',
      'isSelected': true
    },
    {
      'text': 'Apply Leave',
      'icon': 'event_note',
      'image':
          "assets/images/my_business/solutions/solution_organisation_details.png",
      'action': 'apply_leave',
      'isSelected': true
    },
    {
      'text': 'Leave approval',
      'icon': 'event_note',
      'image':
          "assets/images/my_business/solutions/solution_product_details.png",
      'action': 'leave_approval',
      'isSelected': true
    },

    // Additional cards for larger screens

    {
      'text': 'Performance Review',
      'icon': 'assessment',
      'image':
          "assets/images/my_business/solutions/solution_payment_refund.png",
      'action': 'performance_review',
      'isSelected': true
    },
    {
      'text': 'Training & Development',
      'icon': 'school',
      'image': "assets/images/my_business/solutions/solution_my_orders.png",
      'action': 'training_development',
      'isSelected': true
    },
    {
      'text': 'Training & Development',
      'icon': 'school',
      'image': "assets/images/my_business/solutions/solution_my_orders.png",
      'action': 'training_development',
      'isSelected': false
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void toggleFavorite(int index) {
    setState(() {
      actionCardsData[index]['isSelected'] =
          !actionCardsData[index]['isSelected'];
    });
  }

  double gap = 12;
  int labelsPerPage = 2;
  int pageCount = 3;
  final List<Map<String, dynamic>> labelWidgetsData = [
    {'text': 'Latest News', 'action': 'latest_news'},
    {'text': 'Approve/Reject PO', 'action': 'approve_reject_po'},
    {'text': 'Apply Leave', 'action': 'apply_leave'},
    {'text': 'Expense Claims', 'action': 'expense_claims'},
    {'text': 'Timesheet Entry', 'action': 'timesheet_entry'},
    {'text': 'Travel Request', 'action': 'travel_request'},
  ];
  void _onScroll() {
    final double position = _scrollController.offset;
    final double cardExtent = _lastCardWidth + 12; // card width + gap
    final int calculatedPage = (position / cardExtent).round();

    if (calculatedPage != currentPage) {
      setState(() {
        currentPage = calculatedPage;
      });
    }
  }

  double _lastCardWidth = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Chat messages

            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: AppSpacing.xs,
                  ),
                  Center(
                    child: Consumer<AuthProvider>(
                      builder: (context, authProvider, _) {
                        // Get the username from the user profile
                        final String firstName =
                            authProvider.user?.username ?? '';

                        // Get the localized greeting text
                        final String greeting = AppLocalizations.of(context)
                            .translate('home.greeting')
                            .replaceAll('{name}',
                                firstName.isNotEmpty ? firstName : '');

                        return Text(
                          greeting,
                          style: TextStyle(
                              fontSize: 24, fontFamily: 'TiemposText'),
                        );
                      },
                    ),
                  ),

                  // Row of ActionCards
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              HoverableSvgIcon(
                                assetPath:
                                    'assets/images/my_business/home/<USER>',
                              ),
                              SizedBox(
                                width: AppSpacing.xxs,
                              ),
                              Text(
                                AppLocalizations.of(context)
                                    .translate('myBusinessHome.favourites'),
                                style: TextStyle(
                                  fontFamily: 'TiemposText',
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          // SizedBox(width: AppSpacing.sm),
                          SearchIconWithPopup(
                            actionCardsData: actionCardsData,
                            onToggleFavorite: toggleFavorite,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: AppSpacing.sm,
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).size.height /
                            2.8, // adjust as needed
                        child: Builder(
                          builder: (context) {
                            const double gap = 12;

                            final favoriteItems = actionCardsData
                                .where((item) => item['isSelected'] == true)
                                .toList();

                            if (favoriteItems.isEmpty) {
                              return Center(
                                child: Text(
                                  'No favorites selected',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontFamily: 'TiemposText',
                                  ),
                                ),
                              );
                            }

                            final double maxWidth =
                                MediaQuery.of(context).size.width / 1.1;
                            final double totalGapsWidth =
                                gap * (cardsPerRow - 1);
                            final double cardWidth =
                                (maxWidth - totalGapsWidth) / cardsPerRow;

                            return PageView.builder(
                              controller: _pageController,
                              itemCount:
                                  (favoriteItems.length / cardsPerPage).ceil(),
                              itemBuilder: (context, pageIndex) {
                                final int startIndex = pageIndex * cardsPerPage;
                                final int endIndex = (startIndex + cardsPerPage)
                                    .clamp(0, favoriteItems.length);
                                final currentItems =
                                    favoriteItems.sublist(startIndex, endIndex);

                                return Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: List.generate(2, (rowIndex) {
                                    final rowStartIndex =
                                        rowIndex * cardsPerRow;
                                    final rowEndIndex =
                                        rowStartIndex + cardsPerRow;

                                    return Padding(
                                      padding: EdgeInsets.only(
                                          bottom: rowIndex == 0 ? gap : 0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children:
                                            List.generate(cardsPerRow, (i) {
                                          final index = rowStartIndex + i;
                                          if (index >= currentItems.length) {
                                            return SizedBox(
                                              width: cardWidth,
                                              child: SizedBox.shrink(),
                                            );
                                          }

                                          final item = currentItems[index];
                                          final rightGap =
                                              i < cardsPerRow - 1 ? gap : 0.0;

                                          return Padding(
                                            padding: EdgeInsets.only(
                                                right: rightGap),
                                            child: SizedBox(
                                              width: cardWidth,
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height /
                                                  6,
                                              child: ActionCard(
                                                text: item['text'],
                                                imagePath: item['image'],
                                                iconData:
                                                    _getIconData(item['icon']),
                                                onTap: () {
                                                  debugPrint(
                                                      '${item['action']} tapped');
                                                },
                                              ),
                                            ),
                                          );
                                        }),
                                      ),
                                    );
                                  }),
                                );
                              },
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 12),
                      Center(
                        child: SmoothPageIndicator(
                          controller: _actionCardController,
                          count: pageCount, // Always 3 dots
                          effect: WormEffect(
                            dotHeight: 8,
                            dotWidth: 8,
                            spacing: 10,
                            activeDotColor: Colors.blue,
                            dotColor: Colors.grey.shade300,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Label widget example

                  // Column(
                  //   children: [
                  //     Row(
                  //       mainAxisAlignment: MainAxisAlignment.start,
                  //       children: [
                  //         HoverableSvgIcon(
                  //           assetPath:
                  //               'assets/images/my_business/home/<USER>',
                  //         ),
                  //         // SvgPicture.asset(

                  //         //   width: 16,
                  //         //   height: 16,
                  //         //   // colorFilter: ColorFilter.mode(
                  //         //   //     Color(0xff0055FF), BlendMode.srcIn),
                  //         // ),
                  //         SizedBox(
                  //           width: AppSpacing.xxs,
                  //         ),
                  //         Text(
                  //           AppLocalizations.of(context).translate(
                  //               'myBusinessHome.recentUsedSolutions'),
                  //           style: TextStyle(
                  //             fontFamily: 'TiemposText',
                  //             fontSize: 14,
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //     SizedBox(
                  //       height: AppSpacing.sm,
                  //     ),
                  //     Container(
                  //       height: 50,

                  //       // width: MediaQuery.of(context).size.width/0.5, // Adjust height based on your LabelWidget size
                  //       child: PageView.builder(
                  //         controller: _labelPageController,
                  //         itemCount: pageCount,
                  //         itemBuilder: (context, pageIndex) {
                  //           final int startIndex = pageIndex * labelsPerPage;
                  //           final int endIndex = startIndex + labelsPerPage;

                  //           return Row(
                  //             children: List.generate(labelsPerPage, (i) {
                  //               final int index = startIndex + i;
                  //               if (index >= labelWidgetsData.length)
                  //                 return SizedBox.shrink();
                  //               double space = i < labelsPerPage - 1 ? gap : 0;
                  //               return Padding(
                  //                 padding: EdgeInsets.only(right: space),
                  //                 child: LabelWidget(
                  //                   text: labelWidgetsData[index]['text'],
                  //                   space: space,
                  //                   onTap: () {
                  //                     debugPrint(
                  //                         '${labelWidgetsData[index]['action']} tapped');
                  //                   },
                  //                 ),
                  //               );
                  //             }),
                  //           );
                  //         },
                  //       ),
                  //     ),
                  //     const SizedBox(height: 12),
                  //     Center(
                  //       child: SmoothPageIndicator(
                  //         controller: _labelPageController,
                  //         count: pageCount, // Always 3 dots
                  //         effect: WormEffect(
                  //           dotHeight: 8,
                  //           dotWidth: 8,
                  //           spacing: 10,
                  //           activeDotColor: Colors.blue,
                  //           dotColor: Colors.grey.shade300,
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // ),

                  SizedBox(
                    height: AppSpacing.sm,
                  ),
                ],
              ),
            ),

            // Chat field at the bottom
            Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: ChatInputField(
                    chatController: chatController, sendMessage: () {})),
          ],
        ),
      ),
    );
  }
}

// Helper function to convert string icon names to IconData
IconData _getIconData(String iconName) {
  switch (iconName) {
    case 'shopping_cart':
      return Icons.shopping_cart;
    case 'people':
      return Icons.people;
    case 'business':
      return Icons.business;
    case 'event_note':
      return Icons.event_note;
    case 'assessment':
      return Icons.assessment;
    case 'school':
      return Icons.school;
    default:
      return Icons.image; // Default icon
  }
}

class SearchIconWithPopup extends StatefulWidget {
  final List<Map<String, dynamic>> actionCardsData;
  final Function(int) onToggleFavorite;

  const SearchIconWithPopup({
    Key? key,
    required this.actionCardsData,
    required this.onToggleFavorite,
  }) : super(key: key);

  @override
  State<SearchIconWithPopup> createState() => _SearchIconWithPopupState();
}

class _SearchIconWithPopupState extends State<SearchIconWithPopup> {
  bool _isPressed = false;
  final TextEditingController _searchController = TextEditingController();

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => SearchBottomSheet(
          actionCardsData: widget.actionCardsData,
          onToggleFavorite: (index) {
            widget.onToggleFavorite(index);
            setModalState(() {}); // Update bottom sheet UI
          },
          searchController: _searchController,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(right: 4),
      child: GestureDetector(
        onTapDown: (_) => setState(() => _isPressed = true),
        onTapUp: (_) => setState(() => _isPressed = false),
        onTapCancel: () => setState(() => _isPressed = false),
        onTap: _showBottomSheet,
        child: SvgPicture.asset(
          'assets/images/my_business/home/<USER>',
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(
              _isPressed ? Color(0xff0058FF) : Colors.black, BlendMode.srcIn),
        ),
      ),
    );
  }
}

class SearchBottomSheet extends StatefulWidget {
  final List<Map<String, dynamic>> actionCardsData;
  final Function(int) onToggleFavorite;
  final TextEditingController searchController;

  const SearchBottomSheet({
    Key? key,
    required this.actionCardsData,
    required this.onToggleFavorite,
    required this.searchController,
  }) : super(key: key);

  @override
  State<SearchBottomSheet> createState() => _SearchBottomSheetState();
}

class _SearchBottomSheetState extends State<SearchBottomSheet> {
  List<Map<String, dynamic>> _filteredItems = [];
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.actionCardsData);
    widget.searchController.addListener(_filterMenuItems);
    // Auto focus on search field when bottom sheet opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  void _filterMenuItems() {
    setState(() {
      final query = widget.searchController.text.toLowerCase();
      _filteredItems = widget.actionCardsData
          .where((item) => item['text'].toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  void dispose() {
    widget.searchController.removeListener(_filterMenuItems);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Add Favourites Header Row
          Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
            child: Row(
              children: [
                SvgPicture.asset(
                  'assets/images/my_business/home/<USER>',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(Colors.black, BlendMode.srcIn),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Add Favourites',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: EdgeInsets.all(8),
                    child: Icon(
                      Icons.close,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Search Field
          Padding(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.only(bottom: 10),
                    // This wraps the Row and applies the bottom border to the whole block
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.shade300, // Change if needed
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: widget.searchController,
                            focusNode: _focusNode,
                            decoration: InputDecoration(
                              hintText: 'Search',
                              contentPadding: EdgeInsets.zero,
                              border: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              isDense: true,
                              hintStyle: TextStyle(
                                color: Color(0xffD0D0D0),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'TiemposText',
                              ),
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'TiemposText',
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(right: 10.0),
                          child: SvgPicture.asset(
                            'assets/images/my_business/search_collection.svg',
                            width: 20,
                            height: 20,
                            colorFilter:
                                ColorFilter.mode(Colors.black, BlendMode.srcIn),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )),

          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                final originalIndex = widget.actionCardsData.indexOf(item);
                return BottomSheetMenuItem(
                  text: item['text'],
                  isSelected: item['isSelected'] ?? false,
                  onToggleFavorite: () =>
                      widget.onToggleFavorite(originalIndex),
                  onTap: () {
                    debugPrint('${item['action']} tapped');
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class BottomSheetMenuItem extends StatefulWidget {
  final String text;
  final VoidCallback onTap;
  final bool isSelected;
  final VoidCallback onToggleFavorite;

  const BottomSheetMenuItem({
    Key? key,
    required this.text,
    required this.onTap,
    required this.onToggleFavorite,
    this.isSelected = false,
  }) : super(key: key);

  @override
  State<BottomSheetMenuItem> createState() => _BottomSheetMenuItemState();
}

class _BottomSheetMenuItemState extends State<BottomSheetMenuItem> {
  bool isStarPressed = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFF0F0F0), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: widget.onTap,
              child: Text(
                widget.text,
                overflow: TextOverflow.ellipsis,
                softWrap: false,
                maxLines: 1,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTapDown: (_) => setState(() => isStarPressed = true),
            onTapUp: (_) => setState(() => isStarPressed = false),
            onTapCancel: () => setState(() => isStarPressed = false),
            onTap: () {
              widget.onToggleFavorite();
              debugPrint('Star tapped for ${widget.text}');
            },
            child: Container(
              padding: EdgeInsets.all(6),
              child: SvgPicture.asset(
                'assets/images/my_business/home/<USER>',
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  widget.isSelected
                      ? Color(0xff0058FF)
                      : (isStarPressed ? Color(0xff0058FF) : Color(0xffD0D0D0)),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PopupMenuItem extends StatefulWidget {
  final String text;
  final IconData icon;
  final VoidCallback onTap;
  final bool isSelected;

  const PopupMenuItem({
    Key? key,
    required this.text,
    required this.icon,
    required this.onTap,
    this.isSelected = false,
  }) : super(key: key);

  @override
  State<PopupMenuItem> createState() => _PopupMenuItemState();
}

class _PopupMenuItemState extends State<PopupMenuItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isHovered || widget.isSelected
                ? Color(0xFFF5F8FF)
                : Colors.white,
            border: Border(
              bottom: BorderSide(color: Color(0xFFF0F0F0), width: 0.5),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  widget.text,
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
              ),
              SvgPicture.asset(
                'assets/images/my_business/home/<USER>',
                width: 16,
                height: 16,
                colorFilter: ColorFilter.mode(
                  widget.isSelected
                      ? Color(0xff0058FF)
                      : (isHovered ? Color(0xff0058FF) : Color(0xffD0D0D0)),
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HoverableSvgIcon extends StatefulWidget {
  final String assetPath;

  const HoverableSvgIcon({super.key, required this.assetPath});

  @override
  _HoverableSvgIconState createState() => _HoverableSvgIconState();
}

class _HoverableSvgIconState extends State<HoverableSvgIcon> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: () {
        debugPrint('Icon tapped: ${widget.assetPath}');
      },
      child: SvgPicture.asset(
        widget.assetPath,
        width: 16,
        height: 16,
        colorFilter: ColorFilter.mode(
          isPressed ? Color(0xff0055FF) : Colors.black,
          BlendMode.srcIn,
        ),
      ),
    );
  }
}

class LabelWidget extends StatefulWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final double? space;

  const LabelWidget({
    super.key,
    required this.text,
    this.backgroundColor = Colors.white, // Light blue background
    this.textColor = Colors.black,
    this.fontSize = 14,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    this.onTap,
    this.space,
  });

  @override
  State<LabelWidget> createState() => _LabelWidgetState();
}

class _LabelWidgetState extends State<LabelWidget> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        alignment: Alignment.center,
        width: (MediaQuery.of(context).size.width / 2.2) - (widget.space ?? 0),
        // padding: widget.padding,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(AppSpacing.xs),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,

              // blurRadius: 20,
              offset: Offset(0, 1.5),
            ),
          ],
          border: Border.all(
            color: isPressed ? Color(0xff0058FF) : Colors.transparent,
            width: 1,
          ),
        ),
        child: Text(
          widget.text,
          style: TextStyle(
            color: widget.textColor,
            fontSize: widget.fontSize,
            fontWeight: FontWeight.w400,
            fontFamily: 'TiemposText',
          ),
        ),
      ),
    );
  }
}

class ActionCard extends StatefulWidget {
  final String text;
  final String? imagePath;
  final IconData? iconData;
  final VoidCallback? onTap;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final Color? iconColor;
  final double? iconSize;

  const ActionCard({
    super.key,
    required this.text,
    this.imagePath,
    this.iconData,
    this.onTap,
    this.elevation = 1,
    this.padding = const EdgeInsets.all(10.0),
    this.textStyle,
    this.iconColor = Colors.grey,
    this.iconSize = 24,
  });

  @override
  State<ActionCard> createState() => _ActionCardState();
}

class _ActionCardState extends State<ActionCard> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      fontFamily: 'TiemposText',
      color: Colors.black,
    );

    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        // width: 300,
        // height: double.infinity,// Adjust width based on your layout
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Color(0xffD0D0D0),
            width: isPressed ? 1 : 0.5,
          ),
        ),
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image/Icon
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                // height: 100,
                // alignment: Alignment.center,
                child: widget.imagePath != null
                    ? Image.asset(
                        widget.imagePath!,
                        fit: BoxFit.cover,
                      )
                    : Icon(
                        widget.iconData ?? Icons.image,
                        size: widget.iconSize,
                        color: isPressed
                            ? const Color(0xff0058FF)
                            : widget.iconColor,
                      ),
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            // Text
            Expanded(
              flex: 2,
              child: Text(
                widget.text,
                style: widget.textStyle ?? defaultTextStyle,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Optional: Custom dots widget
class SmoothIndicatorWithFixedCount extends StatelessWidget {
  final int count;
  final int currentIndex;

  const SmoothIndicatorWithFixedCount({
    required this.count,
    required this.currentIndex,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SmoothIndicator(
        size: Size(1, 1),
        offset: currentIndex.toDouble(),
        count: 3,
        effect: WormEffect(
          dotHeight: 8,
          dotWidth: 8,
          spacing: 8,
          dotColor: Colors.grey.shade300,
          activeDotColor: Colors.blueAccent,
        ),
      ),
    );
  }
}
