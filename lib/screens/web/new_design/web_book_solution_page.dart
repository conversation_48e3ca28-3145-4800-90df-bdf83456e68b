import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebBookSolutionPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  State<WebBookSolutionPage> createState() => _WebBookSolutionPageState();
}

class _WebBookSolutionPageState extends State<WebBookSolutionPage> {
  List<SolutionItem> solutionItems = [];
  List<String> solutionOptions = [];

  // Hover state variables
  bool _isAddModulesHovering = false;
  bool _isDialogModulesButtonHovering = false;
  bool _isLeftArrowHovering = false;
  bool _isRightArrowHovering = false;
  bool _isCloseButtonHovering = false;
  bool _isCrmAddButtonHovering = false;
  bool _isSalesAddButtonHovering = false;
  final TextEditingController chatController = TextEditingController();

  // State for showing input field

  @override
  void initState() {
    super.initState();
    // Initialize with sample data
    solutionItems = [
      SolutionItem(
        title: 'Create a solution of a Product management',
        lastMessageTime: '18 hours ago',
        date: '22/04/2023',
        versionId: 'V00512',
      ),
      // Add more items as needed
    ];

    // Initialize solution options for the modal
    solutionOptions = List.generate(12, (index) => 'Solutions');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Row(
        children: [
          // Main content
          // Sidebar(),
          Expanded(child: SizedBox()),
          Expanded(
            flex: 9,
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Content
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                    child: _buildSolutionsList(),
                  ),
                ),
                ChatField(
                  isLoading: false,
                  onSendMessage: () {},
                  onFileSelected: (fileName, filePath) {},
                  onToggleRecording: () {},
                  controller: chatController,
                )
              ],
            ),
          ),
          Expanded(child: SizedBox()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 48,
      // padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Back button
          InkWell(
            child: const Icon(Icons.arrow_back, color: Colors.grey, size: 16),
            onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.webMyLibrary;
            },
          ),
          const SizedBox(width: AppSpacing.xs),
          // Book name
          const Text(
            'Book Name',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: AppSpacing.lg),

          // Add Modules dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: EdgeInsets.only(right: 38),
            child: GestureDetector(
              onTap: () {
                _showAddModulesDialog();
              },
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                onEnter: (_) {
                  setState(() {
                    _isAddModulesHovering = true;
                  });
                },
                onExit: (_) {
                  setState(() {
                    _isAddModulesHovering = false;
                  });
                },
                child: Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/add-module.svg',
                      width: 20,
                      height: 20,
                      colorFilter: ColorFilter.mode(
                        _isAddModulesHovering
                            ? const Color(0xff0058FF)
                            : Colors.grey.shade600,
                        BlendMode.srcIn,
                      ),
                    ),
                    const Text(
                      'Add Modules',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 2),
                    Icon(Icons.arrow_drop_down,
                        color: _isAddModulesHovering
                            ? const Color(0xff0058FF)
                            : Colors.grey.shade600),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(width: 20),

          // Agent count
          _buildCountItem('assets/images/agent.svg', '3 Agent (V001)'),

          const SizedBox(width: 16),

          // Objects count
          _buildCountItem('assets/images/cube-box.svg', '12 Objects (V001)'),

          const SizedBox(width: 16),

          // Solutions count
          _buildCountItem(
              'assets/images/square-box-uncheck.svg', '15 Solutions'),

          const Spacer(),

          // Expand button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: SvgPicture.asset(
              'assets/images/expand-arrow-left-new.svg',
              width: 20,
              height: 20,
              fit: BoxFit.fill,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade600,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Row(
        children: [
          SvgPicture.asset(
            iconPath,
            width: 12,
            height: 12,
            colorFilter: ColorFilter.mode(
              Colors.black,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'TiemposText',
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionsList() {
    return ListView.builder(
      itemCount: solutionItems.length,
      itemBuilder: (context, index) {
        final item = solutionItems[index];
        return _buildSolutionItem(item);
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return Container(
      margin: const EdgeInsets.only(
        bottom: 16,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Color(0xffD0D0D0)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xff000000),
                      fontWeight: FontWeight.w600,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Last Message: ${item.lastMessageTime}',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // Right content
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/folder.svg',
                      width: 16,
                      height: 16,
                      colorFilter: ColorFilter.mode(
                        Colors.grey,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      item.versionId,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'TiemposText',
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  item.date,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'TiemposText',
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Show the Add Modules dialog
  void _showAddModulesDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(128),
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(0),
          ),
          clipBehavior: Clip.antiAlias,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.8,
            color: Colors.white,
            child: Stack(
              children: [
                // Close button in top-right corner
                Positioned(
                  right: 10,
                  top: 10,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    onEnter: (_) {
                      setState(() {
                        _isCloseButtonHovering = true;
                      });
                    },
                    onExit: (_) {
                      setState(() {
                        _isCloseButtonHovering = false;
                      });
                    },
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context, rootNavigator: true).pop();
                      },
                      child: Icon(Icons.close,
                          size: 20,
                          color: _isCloseButtonHovering
                              ? const Color(0xff0058FF)
                              : Colors.grey),
                    ),
                  ),
                ),

                // Main content
                Column(
                  children: [
                    Container(
                      height: 50,
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Row(
                        children: [
                          // "Drag Your Solutions" tab
                          Container(
                            width: 205,
                            alignment: Alignment.centerLeft,
                            padding: EdgeInsets.only(left: 10),
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                            child: Text(
                              'Drag Your Solutions',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                          ),

                          // "Modules" tab
                          Container(
                            width: 255,
                            alignment: Alignment.centerLeft,
                            padding: EdgeInsets.only(left: 10),
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Modules',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                                // "+ Modules" button
                                Container(
                                  // width: 255,
                                  alignment: Alignment.center,
                                  child: Container(
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 10),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey.shade900),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      onEnter: (_) {
                                        setState(() {
                                          _isDialogModulesButtonHovering = true;
                                        });
                                      },
                                      onExit: (_) {
                                        setState(() {
                                          _isDialogModulesButtonHovering =
                                              false;
                                        });
                                      },
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.add,
                                              size: 16,
                                              color:
                                                  _isDialogModulesButtonHovering
                                                      ? const Color(0xff0058FF)
                                                      : Colors.grey.shade700),
                                          SizedBox(width: 4),
                                          Text(
                                            'Modules',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color:
                                                  _isDialogModulesButtonHovering
                                                      ? const Color(0xff0058FF)
                                                      : Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          Container(
                            width: 255,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                          ),
                          // Empty space on the right
                          Expanded(
                            child: Container(),
                          ),
                        ],
                      ),
                    ),

                    // Content area
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left panel - Solutions list
                          Container(
                            width: 205,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                            child: _buildSolutionsPanel(),
                          ),

                          // Middle panel - CRM dropdown
                          Container(
                            width: 255,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                          color: Colors.grey.shade300),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.keyboard_arrow_down,
                                              size: 20,
                                              color: Colors.grey.shade800),
                                          SizedBox(width: 4),
                                          Text(
                                            'CRM',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                      MouseRegion(
                                        cursor: SystemMouseCursors.click,
                                        onEnter: (_) {
                                          setState(() {
                                            _isCrmAddButtonHovering = true;
                                          });
                                        },
                                        onExit: (_) {
                                          setState(() {
                                            _isCrmAddButtonHovering = false;
                                          });
                                        },
                                        child: Icon(Icons.add,
                                            size: 16,
                                            color: _isCrmAddButtonHovering
                                                ? const Color(0xff0058FF)
                                                : Colors.grey.shade600),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Right panel - Sales dropdown
                          Container(
                            width: 255,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(color: Colors.grey.shade300),
                              ),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                          color: Colors.grey.shade300),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.keyboard_arrow_down,
                                              size: 20,
                                              color: Colors.grey.shade800),
                                          SizedBox(width: 4),
                                          Text(
                                            'Sales',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.black,
                                              fontFamily: 'TiemposText',
                                            ),
                                          ),
                                        ],
                                      ),
                                      MouseRegion(
                                        cursor: SystemMouseCursors.click,
                                        onEnter: (_) {
                                          setState(() {
                                            _isSalesAddButtonHovering = true;
                                          });
                                        },
                                        onExit: (_) {
                                          setState(() {
                                            _isSalesAddButtonHovering = false;
                                          });
                                        },
                                        child: Icon(Icons.add,
                                            size: 16,
                                            color: _isSalesAddButtonHovering
                                                ? const Color(0xff0058FF)
                                                : Colors.grey.shade600),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Empty panel on the right
                          Expanded(
                            child: Container(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Build the solutions panel with list and navigation
  Widget _buildSolutionsPanel() {
    return Stack(
      children: [
        // Solutions list
        Padding(
          padding:
              const EdgeInsets.only(bottom: 40), // Space for navigation arrows
          child: ListView.builder(
            itemCount: solutionOptions.length,
            itemBuilder: (context, index) {
              return Container(
                padding: EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                decoration: BoxDecoration(
                    // border: Border(
                    //   bottom: BorderSide(color: Colors.grey.shade200),
                    // ),
                    ),
                child: Text(
                  solutionOptions[index],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
              );
            },
          ),
        ),

        // Navigation arrows at the bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 30,
                  height: 30,
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: MouseRegion(
                    onEnter: (_) {
                      setState(() {
                        _isLeftArrowHovering = true;
                      });
                    },
                    onExit: (_) {
                      setState(() {
                        _isLeftArrowHovering = false;
                      });
                    },
                    child: IconButton(
                      iconSize: 14,
                      padding: EdgeInsets.zero,
                      icon: Icon(Icons.chevron_left,
                          color: _isLeftArrowHovering
                              ? const Color(0xff0058FF)
                              : Colors.grey.shade600),
                      onPressed: () {},
                    ),
                  ),
                ),
                Container(
                  width: 30,
                  height: 30,
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: MouseRegion(
                    onEnter: (_) {
                      setState(() {
                        _isRightArrowHovering = true;
                      });
                    },
                    onExit: (_) {
                      setState(() {
                        _isRightArrowHovering = false;
                      });
                    },
                    child: IconButton(
                      iconSize: 14,
                      padding: EdgeInsets.zero,
                      icon: Icon(Icons.chevron_right,
                          color: _isRightArrowHovering
                              ? const Color(0xff0058FF)
                              : Colors.grey.shade600),
                      onPressed: () {},
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class SolutionItem {
  final String title;
  final String lastMessageTime;
  final String date;
  final String versionId;

  SolutionItem({
    required this.title,
    required this.lastMessageTime,
    required this.date,
    required this.versionId,
  });
}
