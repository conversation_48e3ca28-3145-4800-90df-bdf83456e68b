import 'package:flutter/material.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/user_profile_card.dart';

/// A reusable widget that displays a role card with selection state, checkbox, and tooltip.
///
/// This widget is used to display role information in a list, with support for
/// selection, hover effects, and detailed information in a tooltip.
class BuildRoleCard extends StatelessWidget {
  /// The role information to display
  final RoleInfo role;

  /// Whether this role is currently selected
  final bool isSelected;

  /// Callback when the role is tapped
  final Function(RoleInfo) onRoleTap;

  const BuildRoleCard({
    super.key,
    required this.role,
    required this.isSelected,
    required this.onRoleTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        // Highlight for selected role
        color: isSelected ? Color.fromARGB(255, 239, 166, 9) : Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          left: isSelected
              ? BorderSide(color: Colors.blue.shade700, width: 4)
              : BorderSide.none,
        ),
      ),
      child: InkWell(
        onTap: () => onRoleTap(role),
        hoverColor: isSelected
            ? Color(0xFFBBDEFB) // Slightly darker blue on hover
            : Colors.grey.shade100,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Role information
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Role title with tooltip
                    _buildRoleTitleWithTooltip(context),

                    SizedBox(height: 4),

                    // Role description
                    Expanded(
                      child: Text(
                        role.description,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 14,
                          color: isSelected
                              ? Colors.blue.shade700
                              : Colors.grey.shade700,
                          fontFamily: 'TiemposText',
                          height: 1.4,
                          fontWeight:
                              isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the checkbox column on the left side of the card
  Widget _buildCheckboxColumn() {
    return Column(
      children: [
        SizedBox(height: AppSpacing.xs),
        SizedBox(
          width: 20,
          child: Checkbox(
            value: false,
            onChanged: (_) {},
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
            side: BorderSide(
              color: Color(0xFFBEBEBE), // Custom border color
              width: 1, // Custom border width
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the role title with tooltip showing detailed information
  Widget _buildRoleTitleWithTooltip(BuildContext context) {
    return MouseRegion(
      child: Tooltip(
        richMessage: WidgetSpan(
          child: UserProfileCard(
            id: role.id ?? 'so008',
            version: role.version ?? 'V00019',
            displayName: role.title,
            createdBy: role.createdBy ?? 'John Smith',
            createdDate: role.createdDate ?? '10/04/2025',
            modifiedBy: role.modifiedBy ?? 'Jane Doe',
            modifiedDate: role.modifiedDate ?? '28/04/2025',
            roleTitle: role.title,
            roleDescription: role.description,
            width: MediaQuery.of(context).size.width / 3,
          ),
        ),
        preferBelow: true,
        verticalOffset: 20,
        padding: EdgeInsets.zero,
        margin: EdgeInsets.zero,
        showDuration: Duration(seconds: 10),
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [],
        ),
        child: Text(
          "${role.title} ",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            fontFamily: 'TiemposText',
            color: isSelected ? Colors.blue.shade800 : Colors.black,
            decorationColor: Colors.blue.shade700,
            decorationThickness: 1,
          ),
        ),
      ),
    );
  }
}
