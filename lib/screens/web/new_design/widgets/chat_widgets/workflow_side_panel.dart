import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/workflow_data_provider.dart';
import 'package:nsl/theme/spacing.dart';

class WorkflowSidePanel extends StatefulWidget {
  final String workflowId;
  final Function() onClose;
  final Widget ocrButton;

  const WorkflowSidePanel({
    super.key,
    required this.workflowId,
    required this.onClose,
    required this.ocrButton,
  });

  @override
  State<WorkflowSidePanel> createState() => _WorkflowSidePanelState();
}

class _WorkflowSidePanelState extends State<WorkflowSidePanel> {
  Map<String, dynamic>? workflowData;
  bool isLoading = true;
  String selectedSection = 'ownership';
  String? selectedTab;

  // Scroll controller for the main content
  final ScrollController _scrollController = ScrollController();

  // Keys for each section to enable scrolling to them
  final Map<String, GlobalKey> _sectionKeys = {
    'ownership': GlobalKey(),
    'business_rules': GlobalKey(),
    'data_management': GlobalKey(),
    'integrations': GlobalKey(),
    'performance_targets': GlobalKey(),
  };

  @override
  void initState() {
    super.initState();
    _loadWorkflowData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadWorkflowData() async {
    try {
      // Get the workflow data provider
      final workflowDataProvider =
          Provider.of<WorkflowDataProvider>(context, listen: false);

      // Get workflow details from the provider
      final details =
          await workflowDataProvider.getWorkflowDetailsById(widget.workflowId);

      if (mounted) {
        if (details != null) {
          // Set the workflow data and update UI
          setState(() {
            workflowData = details;
            isLoading = false;
            // Set default selected tab for the selected section
            _setDefaultTabForSection(selectedSection);
          });
        } else {
          // No valid data available
          setState(() {
            workflowData = null;
            isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          workflowData = null;
          isLoading = false;
        });
      }
    }
  }

  void _setDefaultTabForSection(String sectionId) {
    // Find the section manually instead of using firstWhere with orElse
    Map<String, dynamic>? section;

    if (workflowData != null && workflowData!['sections'] != null) {
      final sections = workflowData!['sections'] as List;
      for (var s in sections) {
        if (s is Map<String, dynamic> && s['id'] == sectionId) {
          section = s;
          break;
        }
      }
    }

    if (section != null &&
        section['tabs'] != null &&
        (section['tabs'] as List).isNotEmpty) {
      setState(() {
        selectedTab = section!['tabs'][0]['id'];
      });
    } else {
      setState(() {
        selectedTab = null;
      });
    }
  }

  void _selectSection(String sectionId) {
    setState(() {
      selectedSection = sectionId;
      _setDefaultTabForSection(sectionId);
    });

    // Scroll to the selected section
    if (_sectionKeys.containsKey(sectionId)) {
      final RenderObject? renderObject =
          _sectionKeys[sectionId]?.currentContext?.findRenderObject();
      if (renderObject != null) {
        _scrollController.position.ensureVisible(
          renderObject,
          alignment: 0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _selectTab(String tabId) {
    setState(() {
      selectedTab = tabId;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (workflowData == null) {
      return const Center(child: Text('No workflow data available'));
    }

    // Add null check for sections
    final hasSections = workflowData!.containsKey('sections') &&
        workflowData!['sections'] != null;
    final sections =
        hasSections ? workflowData!['sections'] as List : <dynamic>[];

    return Container(
      width: 400,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          // BoxShadow(
          //   color: Colors.black.withValues(alpha: 26), // 0.1 * 255 = ~26
          //   blurRadius: 10,
          //   offset: const Offset(-5, 0),
          // ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.only(
                left: AppSpacing.xxl,
                right: AppSpacing.xxs,
                bottom: AppSpacing.xxs,
                top: AppSpacing.xxs),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),

                        //   const Color.fromARGB(255, 92, 163, 234),
                        radius: 10,
                        child: Icon(
                          Icons.person_outline,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: Text(
                          workflowData!['mainTitle'] ?? 'Workflow Details',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            fontFamily: 'TimeposText',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.chat),
                  onPressed: widget.onClose,
                  iconSize: 20,
                ),
                widget.ocrButton,
              ],
            ),
          ),

          // Navigation and content
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left navigation

                Container(
                  width: 40,
                  padding: EdgeInsets.only(top: 3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // OA - Ownership & Authority
                      Padding(
                        padding: EdgeInsets.only(left: 8, top: 16),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _selectSection('ownership'),
                            child: Text(
                              'OA',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: selectedSection == 'ownership'
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align with next section
                      SizedBox(height: 10),

                      // BR - Business Rules
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _selectSection('business_rules'),
                            child: Text(
                              'BR',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: selectedSection == 'business_rules'
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align with next section
                      SizedBox(height: 10),

                      // DM - Data Management
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _selectSection('data_management'),
                            child: Text(
                              'DM',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: selectedSection == 'data_management'
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align with next section
                      SizedBox(height: 10),

                      // IN - Integrations
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _selectSection('integrations'),
                            child: Text(
                              'IN',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: selectedSection == 'integrations'
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align with next section
                      SizedBox(height: 10),

                      // PR - Performance Targets
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _selectSection('performance_targets'),
                            child: Text(
                              'PR',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: selectedSection == 'performance_targets'
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // child: Column(
                //   children: [
                //     for (final section in sections)
                //       _buildSectionIcon(
                //         section['id'],
                //         section['title'],
                //         selectedSection == section['id'],
                //       ),
                //   ],
                // ),
                // ),

                // Content area with all sections in a single scroll view
                Expanded(
                  child: sections.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.grey.shade400,
                                  size: 48,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No workflow sections available',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'This workflow does not have any sections defined in the API response.',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : SingleChildScrollView(
                          controller: _scrollController,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              for (final section in sections)
                                _buildSectionWithKey(section),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Removed unused _buildSectionIcon method

  // Build a section with a key for scrolling
  Widget _buildSectionWithKey(Map<String, dynamic> sectionData) {
    final String sectionId = sectionData['id'];

    return Container(
      key: _sectionKeys[sectionId],
      decoration: BoxDecoration(
          // border: Border(
          //   bottom: BorderSide(color: Colors.grey.shade300),
          // ),
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Container(
            padding: const EdgeInsets.only(
                left: AppSpacing.sm, right: AppSpacing.sm, top: AppSpacing.md),
            color: Colors.white,
            child: Text(
              sectionData['title'],
              style: const TextStyle(
                fontSize: 10,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Tabs if available
          if (sectionData['tabs'] != null &&
              (sectionData['tabs'] as List).isNotEmpty)
            Container(
              height: 40,
              decoration: BoxDecoration(
                  // border: Border(
                  //   bottom: BorderSide(color: Colors.grey.shade300),
                  // ),
                  ),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  for (final tab in sectionData['tabs'])
                    _buildTabButton(tab['id'], tab['title']),
                ],
              ),
            ),

          // Content
          Padding(
            padding: const EdgeInsets.only(
                left: AppSpacing.md, right: AppSpacing.md),
            child: _buildContentForSection(sectionData),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(String id, String title) {
    final isSelected = selectedTab == id;

    return InkWell(
      onTap: () => _selectTab(id),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
        alignment: Alignment.center,
        // Remove the border from the container
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Text with bottom padding to make room for the underline
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                fontFamily: 'TimeposText',
                color: Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            // Custom underline that only spans the width of the text
            Container(
              height: 2,
              width:
                  title.length * 5.0, // Approximate width based on text length
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.transparent,
              margin: EdgeInsets.only(top: 2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentForSection(Map<String, dynamic> sectionData) {
    // Add null check for content
    final content =
        sectionData.containsKey('content') && sectionData['content'] != null
            ? sectionData['content'] as Map<String, dynamic>
            : <String, dynamic>{};

    switch (sectionData['id']) {
      case 'ownership':
        return _buildOwnershipContent(content);
      case 'business_rules':
        return _buildBusinessRulesContent(content);
      case 'data_management':
        return _buildDataManagementContent(content);
      case 'integrations':
        return _buildIntegrationsContent(content);
      case 'performance_targets':
        return _buildPerformanceTargetsContent(content);
      default:
        return const Text('Content not available');
    }
  }

  Widget _buildOwnershipContent(Map<String, dynamic> content) {
    // Add null checks for content
    final description = content.containsKey('description')
        ? content['description'] ?? 'No description available'
        : 'No description available';
    final items = content.containsKey('items') && content['items'] != null
        ? content['items'] as List
        : <dynamic>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          description,
          style: const TextStyle(fontSize: 14, fontFamily: 'TimposText'),
        ),
        const SizedBox(height: AppSpacing.sm),
        for (final item in items)
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.xxs),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                    fontFamily: 'TimposText',
                  ),
                ),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.normal,
                          height: 1.5,
                          fontSize: 14),
                      children: [
                        TextSpan(
                          text: '${item['title'] ?? ''} ',
                          style: const TextStyle(
                            fontWeight: FontWeight.normal,
                            fontSize: 14,
                            color: Colors.black,
                          ),
                        ),
                        TextSpan(
                          text: item['description'] ?? '',
                          style: TextStyle(
                            fontWeight: FontWeight.normal,
                            fontSize: 14,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBusinessRulesContent(Map<String, dynamic> content) {
    // Add null checks for content
    final rules = content.containsKey('rules') && content['rules'] != null
        ? content['rules'] as List
        : content.containsKey('items') && content['items'] != null
            ? content['items'] as List
            : <dynamic>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (final rule in rules)
          Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  rule['title'] ?? rule['rule'] ?? 'Business Rule',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    fontFamily: 'TiemposText',
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                if (rule.containsKey('implementation') &&
                    rule['implementation'] != null)
                  _buildRuleItem('Implemented by', rule['implementation']),
                if (rule.containsKey('enforced_by') &&
                    rule['enforced_by'] != null)
                  _buildRuleItem('Enforced by ${rule['enforced_by']}', ''),
                if (rule.containsKey('condition') &&
                    rule['condition'] != null &&
                    rule.containsKey('action') &&
                    rule['action'] != null)
                  _buildRuleItem('${rule['condition']}', '→ ${rule['action']}'),
                if (rule.containsKey('entity') && rule['entity'] != null)
                  _buildRuleItem('Entity', rule['entity']),
                if (rule.containsKey('description') &&
                    rule['description'] != null)
                  _buildRuleItem('Description', rule['description']),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRuleItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '• ',
            style: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 14,
                color: Colors.black,
                fontFamily: 'TimposText'),
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: const TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black,
                    fontFamily: 'TimposText'),
                children: [
                  TextSpan(
                    text: label,
                    style: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14,
                        height: 1.5,
                        color: Colors.black,
                        fontFamily: 'TimposText'),
                  ),
                  if (value.isNotEmpty) TextSpan(text: ': $value'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagementContent(Map<String, dynamic> content) {
    // Add null checks for content
    final entityOperations = content.containsKey('entity_operations') &&
            content['entity_operations'] != null
        ? content['entity_operations'] as List
        : content.containsKey('items') && content['items'] != null
            ? content['items'] as List
            : <dynamic>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        const Text(
          'Entity Operations:',
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TimposText'),
        ),
        const SizedBox(height: AppSpacing.sm),
        if (entityOperations.isEmpty)
          const Text(
            'No entity operations defined',
            style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
                fontFamily: 'TimposText'),
          )
        else
          for (final entity in entityOperations)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.md, left: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '- ',
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TimposText'),
                      ),
                      Expanded(
                        child: RichText(
                          text: TextSpan(
                            style: const TextStyle(
                                fontSize: 14,
                                height: 1.5,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText'),
                            children: [
                              TextSpan(
                                text: entity['entity'] ??
                                    entity['title'] ??
                                    'Entity',
                                style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.normal,
                                    color: Colors.black,
                                    fontFamily: 'TimposText'),
                              ),
                              TextSpan(
                                text:
                                    ' ${entity['operations'] ?? entity['description'] ?? ''}',
                                style: TextStyle(
                                  height: 1.5,
                                  fontSize: 14,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.black,
                                  fontFamily: 'TimposText',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
      ],
    );
  }

  Widget _buildIntegrationsContent(Map<String, dynamic> content) {
    // Add null checks for content
    if (selectedTab == 'process_relationships_tab') {
      final inputs = content.containsKey('inputs') && content['inputs'] != null
          ? content['inputs'] as List
          : <dynamic>[];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Inputs from:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TimposText',
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          if (inputs.isEmpty)
            const Text(
              'No inputs defined',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
                fontFamily: 'TimposText',
              ),
            )
          else
            for (final input in inputs)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: RichText(
                  text: TextSpan(
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: Colors.black,
                      fontFamily: 'TimposText',
                    ),
                    children: [
                      TextSpan(
                        text: input['from'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                          fontFamily: 'TimposText',
                        ),
                      ),
                      if (input.containsKey('process') &&
                          input['process'] != null)
                        TextSpan(
                          text: ' ${input['process']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TimposText',
                          ),
                        ),
                      if (input.containsKey('description') &&
                          input['description'] != null)
                        TextSpan(
                          text: ' ${input['description']}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TimposText',
                          ),
                        ),
                    ],
                  ),
                ),
              ),
        ],
      );
    } else {
      // Check for systems or items
      final systems =
          content.containsKey('systems') && content['systems'] != null
              ? content['systems'] as List
              : content.containsKey('items') && content['items'] != null
                  ? content['items'] as List
                  : <dynamic>[];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (systems.isEmpty)
            const Text(
              'No integrations defined',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
                fontFamily: 'TimposText',
              ),
            )
          else
            for (final system in systems)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                        fontFamily: 'TimposText',
                      ),
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TimposText',
                          ),
                          children: [
                            TextSpan(
                              text:
                                  '${system['name'] ?? system['title'] ?? 'System'}: ',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                            TextSpan(
                              text: system['description'] ??
                                  system['integration_type'] ??
                                  '',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        ],
      );
    }
  }

  Widget _buildPerformanceTargetsContent(Map<String, dynamic> content) {
    // Add null checks for content
    if (selectedTab == 'performance_monitoring_tab') {
      final monitoring =
          content.containsKey('monitoring') && content['monitoring'] != null
              ? content['monitoring'] as List
              : content.containsKey('items') && content['items'] != null
                  ? content['items'] as List
                  : <dynamic>[];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (monitoring.isEmpty)
            const Text(
              'No monitoring data defined',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
                fontFamily: 'TimposText',
              ),
            )
          else
            for (final item in monitoring)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                          fontFamily: 'TimposText',
                        )),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            fontFamily: 'TimposText',
                          ),
                          children: [
                            TextSpan(
                              text: '${item['name'] ?? item['title'] ?? ''} ',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                            TextSpan(
                              text: item['value'] ?? item['description'] ?? '',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        ],
      );
    } else {
      final targets =
          content.containsKey('targets') && content['targets'] != null
              ? content['targets'] as List
              : content.containsKey('items') && content['items'] != null
                  ? content['items'] as List
                  : <dynamic>[];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (targets.isEmpty)
            const Text(
              'No performance targets defined',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
                fontFamily: 'TimposText',
              ),
            )
          else
            for (final target in targets)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: Colors.black,
                        fontFamily: 'TimposText',
                      ),
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TimposText',
                          ),
                          children: [
                            TextSpan(
                              text:
                                  '${target['name'] ?? target['title'] ?? ''}: ',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                            TextSpan(
                              text: target['target'] ??
                                  target['value'] ??
                                  target['description'] ??
                                  '',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                                fontFamily: 'TimposText',
                              ),
                            ),
                            if (target.containsKey('code') &&
                                target['code'] != null &&
                                target['code'].toString().isNotEmpty)
                              TextSpan(
                                text: ' ${target['code']}',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.normal,
                                  color: Colors.black,
                                  fontFamily: 'TimposText',
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        ],
      );
    }
  }
}
