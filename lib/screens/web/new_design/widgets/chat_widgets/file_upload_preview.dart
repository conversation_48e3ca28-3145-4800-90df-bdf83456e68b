import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A widget that displays a preview of an uploaded file with a close button
class FileUploadPreview extends StatelessWidget {
  /// The name of the file
  final String fileName;

  /// Callback when the close button is pressed
  final VoidCallback onClose;

  final VoidCallback onFileTap;

  /// Constructor
  const FileUploadPreview({
    super.key,
    required this.fileName,
    required this.onClose,
    required this.onFileTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File icon with overlapping close button
          Stack(
            clipBehavior: Clip.none,
            children: [
              // File icon
              InkWell(
                onTap: onFileTap,
                child: SvgPicture.asset(
                  'assets/images/chat/file_upload.svg',
                  width: 80,
                  height: 80,
                ),
              ),

              // Close button positioned at the top-right corner
              Positioned(
                top: -8,
                left: -8,
                child: InkWell(
                  onTap: onClose,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      'assets/images/chat/close.svg',
                      width: 22,
                      height: 22,
                    ),
                  ),
                ),
              ),
            ],
          ),

          // File name below the icon
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: SizedBox(
              width: 120, // Limit width to keep it compact
              child: Text(
                fileName,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
