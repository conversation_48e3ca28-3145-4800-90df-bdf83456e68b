import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_preview.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/add_button_with_menu.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/hover_button_widget.dart';
import 'package:nsl/theme/spacing.dart';

import '../../../../../utils/logger.dart';
import '../../../../../services/multimedia_service.dart';
import 'speech_recognition_widget.dart';

class ChatField extends StatefulWidget {
  final double? width;
  final double? height;
  final bool isLoading;
  final Function() onSendMessage;
  final Function()? onCancelRequest;
  final Function(String, String) onFileSelected;
  final Function() onToggleRecording;
  final TextEditingController? controller;
  final MultimediaService? multimediaService;
  final String? initialUploadedFileName;
  final String? initialUploadedFileText;
  final bool initialIsFileUploaded;
  final VoidCallback? onFileUploadTap;
  final VoidCallback? onFileCloseTap;
  final dynamic parentState;
  Widget? extraWidgets;

  ChatField({
    Key? key,
    this.width,
    this.height,
    this.parentState,
    required this.isLoading,
    required this.onSendMessage,
    this.onCancelRequest,
    required this.onFileSelected,
    required this.onToggleRecording,
    this.controller,
    this.multimediaService,
    this.initialUploadedFileName,
    this.initialUploadedFileText,
    this.initialIsFileUploaded = false,
    this.onFileUploadTap,
    this.onFileCloseTap,
    this.extraWidgets,
  }) : super(key: key);

  @override
  State<ChatField> createState() => _ChatFieldState();
}

class _ChatFieldState extends State<ChatField> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isRecording = false;
  String _recognizedText = '';
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String uploadedFileText = '';
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    chatController = widget.controller ?? TextEditingController();
    _multimediaService = widget.multimediaService ?? MultimediaService();

    // Initialize file upload state if provided
    isFileUploaded = widget.initialIsFileUploaded;
    uploadedFileName = widget.initialUploadedFileName ?? '';
    uploadedFileText = widget.initialUploadedFileText ?? '';

    // Listen for text changes to update hasTextInChatField
    chatController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    // Only dispose the controller if we created it internally
    if (widget.controller == null) {
      chatController.dispose();
    }
    chatController.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ChatField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update file upload state when parent widget changes
    // if (widget.initialIsFileUploaded != oldWidget.initialIsFileUploaded ||
    //     widget.initialUploadedFileName != oldWidget.initialUploadedFileName ||
    //     widget.initialUploadedFileText != oldWidget.initialUploadedFileText) {

    // }
    setState(() {
      isFileUploaded = widget.initialIsFileUploaded;
      uploadedFileName = widget.initialUploadedFileName ?? '';
      uploadedFileText = widget.initialUploadedFileText ?? '';
    });

    Logger.info('ChatField: File upload state updated from parent');
  }

  void _onTextChanged() {
    // Force a rebuild when text changes to update UI
    setState(() {});
  }

  bool get hasTextInChatField => chatController.text.trim().isNotEmpty;

  void _sendMessage() {
    widget.onSendMessage();
  }

  void _cancelRequest() {
    if (widget.onCancelRequest != null) {
      widget.onCancelRequest!();
    }
  }

  void _toggleRecording() {
    setState(() {
      isRecording = !isRecording;
    });
    widget.onToggleRecording();
  }

// Build a full-width recording UI for web audio recorder
  Widget _buildFullWidthRecordingUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: _multimediaService.createWebAudioRecorderWidget(
        chatController: chatController,
        onCancel: () {
          setState(() {
            isRecording = false;
          });
        },
        onLoadingChanged: (loading) {
          widget.parentState.setState(() {
            widget.parentState.isLoading = loading;
          });
        },
      ),
    );
  }

  Widget hoverButtons({required Widget icon, required Function() onPressed}) {
    return HoverButton(
      icon: icon,
      onPressed: onPressed,
    );
  }

  @override
  Widget build(BuildContext context) {
    // If recording is active, show a full-width recording UI
    if (isRecording && _multimediaService.shouldUseWebAudioRecorder()) {
      return Container(
        margin: EdgeInsets.only(
          bottom: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.md),
          border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
        ),
        constraints: BoxConstraints(
          maxHeight: 400,
          minHeight: widget.height ?? 110,
        ),
        child: _buildFullWidthRecordingUI(context),
      );
    }

    // Otherwise, show the normal chat field
    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      constraints: BoxConstraints(
        maxHeight: 400,
        minHeight: widget.height ?? 110,
      ),
      padding: EdgeInsets.only(
          top: AppSpacing.xxs, left: AppSpacing.xxs, right: AppSpacing.xxs),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show file upload preview if a file is uploaded
          if (isFileUploaded)
            FileUploadPreview(
              fileName: uploadedFileName,
              onFileTap: widget.onFileUploadTap ?? () {},
              onClose: () {
                setState(() {
                  isFileUploaded = false;
                  uploadedFileName = '';
                  uploadedFileText = '';
                  // Clear the text field if it contains the OCR text
                  if (chatController.text == uploadedFileText) {
                    chatController.clear();
                  }
                  // if (widget.onFileCloseTap != null) {
                  //   widget.onFileCloseTap!();
                  // }
                });
              },
            ),

          Flexible(
            child: KeyboardListener(
              //focusNode: _focusNode,
              onKeyEvent: (KeyEvent event) {
                if (event is KeyDownEvent &&
                    event.logicalKey == LogicalKeyboardKey.enter) {
                  if (!widget.isLoading) {
                    _sendMessage();
                  }
                }
              },
              focusNode: _focusNode,
              child: TextField(
                cursorHeight: 14,
                controller: chatController,
                maxLines: null,
                enabled: !widget.isLoading,
                decoration: InputDecoration(
                  focusColor: Colors.transparent,
                  hintStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey,
                    fontFamily: "TiemposText",
                  ),
                  hintText: widget.isLoading
                      ? AppLocalizations.of(context)
                          .translate('home.sendingMessage')
                      : AppLocalizations.of(context).translate('home.askNSL'),
                  hoverColor: Colors.transparent,
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  enabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  focusedBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                  disabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                ),
                onSubmitted: widget.isLoading ? null : (_) => _sendMessage(),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // Add button for file upload
                  AddButtonWithMenu(widget.parentState),
                  SizedBox(width: AppSpacing.xs),
                  if (widget.extraWidgets != null) widget.extraWidgets!,
                ],
              ),

              // Show either mic button or arrow button based on text state
              if (hasTextInChatField)
                // Show arrow button when there's text
                hoverButtons(
                  icon: widget.isLoading
                      ? Icon(Icons.stop) // Show stop icon when loading
                      : Icon(Icons.arrow_upward),
                  onPressed: widget.isLoading ? _cancelRequest : _sendMessage,
                )
              else if (isRecording &&
                  !_multimediaService.shouldUseWebAudioRecorder())
                // Show speech recognition UI when recording (non-web platforms only)
                Row(
                  children: [
                    SpeechRecognitionWidget(
                      recognizedText: _recognizedText,
                      onCancel: () {
                        _multimediaService.stopSpeechRecognition();
                      },
                      onConfirm: () {
                        // Get the latest recognized text directly from the service
                        final latestText =
                            _multimediaService.getRecognizedText();

                        if (latestText.isNotEmpty) {
                          setState(() {
                            // Update the chat field with the latest recognized text
                            chatController.text = latestText;

                            // Also update our local state
                            _recognizedText = latestText;
                          });

                          Logger.info(
                              "Setting chat field text to: $latestText");
                        } else {
                          Logger.info("No recognized text available");
                        }

                        _multimediaService.stopSpeechRecognition();
                      },
                    ),
                  ],
                )
              else
                // Show mic button when there's no text and not recording
                hoverButtons(
                    icon: widget.isLoading
                        ? Icon(Icons.stop)
                        : Icon(Icons.mic_none),
                    onPressed:
                        widget.isLoading ? _cancelRequest : _toggleRecording),
            ],
          )
        ],
      ),
    );
  }
}
