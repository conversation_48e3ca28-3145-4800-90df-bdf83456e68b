import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/my_library/books_page.dart';

import 'package:nsl/widgets/json_model_parser.dart';


class AppDataLoader extends StatefulWidget {
  const AppDataLoader({super.key});

  @override
  State<AppDataLoader> createState() => _AppDataLoaderState();
}

class _AppDataLoaderState extends State<AppDataLoader> {
  late Future<AppData> _dataFuture;

  @override
  void initState() {
    super.initState();
    _dataFuture = _loadAppData();
  }

  Future<AppData> _loadAppData() async {
    // In a real app, this would load from an API or asset file
    // For this example, we'll use a string constant
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    
    // This is where you'd typically load the JSON from an asset or API
    // String jsonString = await rootBundle.loadString('assets/app_data.json');
    
    // Using the constant string of our comprehensive JSON
    final jsonString = '''
{
  "appMetrics": {
    "booksCount": 12,
    "solutionsCount": 35,
    "objectsCount": 102
  },
  "navigation": {
    "items": [
      {
        "id": "1",
        "icon": "chat_bubble_outline",
        "route": "/chat",
        "isSelected": false
      },
      {
        "id": "2",
        "icon": "apps",
        "route": "/apps",
        "isSelected": true
      },
      {
        "id": "3",
        "icon": "work_outline",
        "route": "/work",
        "isSelected": false
      },
      {
        "id": "4", 
        "icon": "description_outlined",
        "route": "/documents",
        "isSelected": false
      },
      {
        "id": "5",
        "icon": "calendar_today",
        "route": "/calendar",
        "isSelected": false
      },
      {
        "id": "6",
        "icon": "notifications_none",
        "route": "/notifications",
        "isSelected": false
      },
      {
        "id": "7",
        "icon": "person_outline",
        "route": "/profile",
        "isSelected": false
      }
    ]
  },
  "books": {
    "pageTitle": "My Books",
    "createButtonText": "Create Book",
    "items": [
      {
        "id": "1",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Ecommerce",
        "isDraft": false
      },
      {
        "id": "2",
        "title": "Fashion & Apparel",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fashion",
        "isDraft": false
      },
      {
        "id": "3",
        "title": "Financial Advisory",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Finance",
        "isDraft": false
      },
      {
        "id": "4",
        "title": "Home Rentals",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Rentals",
        "isDraft": true
      },
      {
        "id": "5",
        "title": "Online Grocery",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Grocery",
        "isDraft": false
      },
      {
        "id": "6",
        "title": "Courier & Logistics",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Logistics",
        "isDraft": false
      },
      {
        "id": "7",
        "title": "Automotive",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Automotive",
        "isDraft": true
      },
      {
        "id": "8",
        "title": "Fitness & Wellness",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fitness",
        "isDraft": false
      },
      {
        "id": "9",
        "title": "Real Estate",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Real+Estate",
        "isDraft": false
      },
      {
        "id": "10",
        "title": "Restaurant & Cafe",
        "categoryType": "B2C",
        "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Restaurant",
        "isDraft": false
      }
    ]
  },
  "solutions": {
    "pageTitle": "My Solutions",
    "createButtonText": "Create Solution",
    "items": [
      {
        "id": "1",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "2",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "3",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "4",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "5",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "6",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "7",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "8",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "9",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      },
      {
        "id": "10",
        "title": "Ecommerce",
        "categoryType": "B2C",
        "version": "V00172"
      }
    ]
  },
  "objects": {
    "pageTitle": "My Objects",
    "createButtonText": "Create Object",
    "items": [
      {
        "id": "1",
        "title": "Customer",
        "version": "V00172",
        "icon": "person"
      },
      {
        "id": "2",
        "title": "Product",
        "version": "V00172",
        "icon": "shopping_bag"
      },
      {
        "id": "3",
        "title": "Address",
        "version": "V00172",
        "icon": "location_on"
      },
      {
        "id": "4",
        "title": "Employee",
        "version": "V00172",
        "icon": "badge"
      },
      {
        "id": "5",
        "title": "New Launch",
        "version": "V00172",
        "icon": "rocket_launch"
      },
      {
        "id": "6",
        "title": "Customer",
        "version": "V00172",
        "icon": "person"
      },
      {
        "id": "7",
        "title": "Product",
        "version": "V00172",
        "icon": "shopping_bag"
      },
      {
        "id": "8",
        "title": "Address",
        "version": "V00172",
        "icon": "location_on"
      },
      {
        "id": "9",
        "title": "Employee",
        "version": "V00172",
        "icon": "badge"
      },
      {
        "id": "10",
        "title": "New Launch",
        "version": "V00172",
        "icon": "rocket_launch"
      }
    ]
  },
  "gridLayout": {
    "columns": 12,
    "gutterWidth": 16,
    "columnWidth": 80,
    "maxItemsPerRow": 5,
    "cardPadding": 12,
    "sectionPadding": 16
  },
  "styles": {
    "colors": {
      "primary": "#3498DB",
      "secondary": "#2C3E50",
      "background": "#F8F9FA",
      "card": "#FFFFFF",
      "border": "#E2E8F0",
      "text": {
        "primary": "#2D3748",
        "secondary": "#718096",
        "muted": "#A0AEC0"
      },
      "badge": {
        "draft": "#FFB900"
      }
    },
    "radius": {
      "small": 4,
      "medium": 8,
      "large": 12
    }
  }
}
    ''';
    
    return AppData.loadFromJsonFile(jsonString);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<AppData>(
      future: _dataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text('Error loading data: ${snapshot.error}'),
            ),
          );
        }
        
        final AppData appData = snapshot.data!;
        return BooksPages();
      },
    );
  }
}

class NavigationScreen extends StatefulWidget {
  final AppData appData;
  
  const NavigationScreen({super.key, required this.appData});

  @override
  State<NavigationScreen> createState() => _NavigationScreenState();
}

class _NavigationScreenState extends State<NavigationScreen> {
  late int _selectedIndex;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    // Find initially selected nav item from the JSON
    _selectedIndex = widget.appData.navigation.items
        .indexWhere((item) => item.isSelected);
    
    // Default to index 0 if none is selected
    if (_selectedIndex < 0) _selectedIndex = 0;
    
    _pageController = PageController(initialPage: _selectedIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side Navigation
          SideNavBar(
            navItems: widget.appData.navigation.items,
            selectedIndex: _selectedIndex,
            onTap: _onNavItemTapped,
          ),
          
          // Main Content with PageView for swiping
          Expanded(
            child: SwipeDetector(
              onSwipe: (direction) {
                if (direction == Direction.left && _selectedIndex < widget.appData.navigation.items.length - 1) {
                  _onNavItemTapped(_selectedIndex + 1);
                } else if (direction == Direction.right && _selectedIndex > 0) {
                  _onNavItemTapped(_selectedIndex - 1);
                }
              },
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(), // Disabling PageView's built-in swipe
                onPageChanged: (index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                },
                children: [
                  // Placeholder screens - replace with your actual pages
                  const Scaffold(body: Center(child: Text('Chat Screen'))),
                  BooksPage(appData: widget.appData),
                  const Scaffold(body: Center(child: Text('Work Screen'))),
                  SolutionsPage(appData: widget.appData),
                  const Scaffold(body: Center(child: Text('Calendar Screen'))),
                  const Scaffold(body: Center(child: Text('Notifications Screen'))),
                  ObjectsPage(appData: widget.appData),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Placeholder for the BooksPage
class BooksPage extends StatelessWidget {
  final AppData appData;
  
  const BooksPage({super.key, required this.appData});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body:BooksPages()
    );
  }
}

// Placeholder for the SolutionsPage
class SolutionsPage extends StatelessWidget {
  final AppData appData;
  
  const SolutionsPage({super.key, required this.appData});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('Solutions Page: ${appData.solutions.pageTitle}'),
      ),
    );
  }
}

// Placeholder for the ObjectsPage
class ObjectsPage extends StatelessWidget {
  final AppData appData;
  
  const ObjectsPage({super.key, required this.appData});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('Objects Page: ${appData.objects.pageTitle}'),
      ),
    );
  }
}

// SideNavBar Component
class SideNavBar extends StatelessWidget {
  final List<NavItem> navItems;
  final int selectedIndex;
  final Function(int) onTap;
  
  const SideNavBar({
    super.key,
    required this.navItems,
    required this.selectedIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          // Logo
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                'nb',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Nav items
          Expanded(
            child: ListView.builder(
              itemCount: navItems.length,
              itemBuilder: (context, index) {
                return _buildNavItem(
                  context,
                  navItems[index],
                  index,
                  isSelected: index == selectedIndex,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(BuildContext context, NavItem navItem, int index, {bool isSelected = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () => onTap(index),
        icon: Icon(
          navItem.getIcon(),
          color: isSelected ? Colors.blue : Colors.grey.shade600,
        ),
      ),
    );
  }
}

// Add gesture detector wrapper for mobile swipe navigation
class SwipeDetector extends StatelessWidget {
  final Widget child;
  final Function(Direction) onSwipe;

  const SwipeDetector({
    super.key,
    required this.child,
    required this.onSwipe,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity! > 0) {
          // Right swipe
          onSwipe(Direction.right);
        } else if (details.primaryVelocity! < 0) {
          // Left swipe
          onSwipe(Direction.left);
        }
      },
      child: child,
    );
  }
}

enum Direction { left, right, up, down }
