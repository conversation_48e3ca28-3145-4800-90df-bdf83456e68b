import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/preview_screen.dart';

import 'package:nsl/screens/web/new_design/nsl_java_converter.dart';
import 'package:nsl/screens/web/new_design/web_book_detail_page.dart';
import 'package:nsl/screens/web/new_design/web_book_solution_page.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat.dart';
import 'package:nsl/screens/web/new_design/web_my_library_screen.dart';
import 'package:nsl/screens/web/new_design/web_object_screen.dart';
import 'package:nsl/screens/web/new_design/web_solutions_screen.dart';

import 'package:nsl/screens/web/new_design/widgets/sidebar.dart';
import 'package:nsl/screens/web_transaction/web_collection_widgets.dart';
import 'package:nsl/screens/web_transaction/web_home_transaction.dart';
import 'package:nsl/screens/web_transaction/web_transaction_collection.dart';
import 'package:nsl/screens/web_transaction/web_transaction_solution.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/utils/logger.dart';

import 'package:provider/provider.dart';

class WebHomeScreenNew extends StatefulWidget {
  const WebHomeScreenNew({super.key});

  @override
  State<WebHomeScreenNew> createState() => _WebHomeScreenNewState();
}

// RoleInfo model is now imported from lib/models/role_info.dart

// EntityAttribute and EntityInfo are now replaced by Entity and Attribute from EntitiesData model

// Using EntityGroup directly from EntitiesData model

class _WebHomeScreenNewState extends State<WebHomeScreenNew> {
  @override
  Widget build(BuildContext context) {
    // Provider is now used directly in the Sidebar component
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Row(
        children: [Sidebar(), Expanded(child: mainContent())],
      ),
    );
  }

  Widget mainContent() {
    final webHomeProvider = Provider.of<WebHomeProvider>(context);

    // Get the current screen index from the WebHomeProvider
    final currentScreenIndex = webHomeProvider.currentScreenIndex;

    // Log the current screen index
    Logger.info('Current screen index in mainContent: $currentScreenIndex');

    // If the current screen index is empty, set it to home
    if (currentScreenIndex.isEmpty) {
      // Use a post-frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          webHomeProvider.currentScreenIndex = ScreenConstants.home;
          Logger.info('Screen index was empty, set to home');
        }
      });
    }

    switch (currentScreenIndex) {
      case ScreenConstants.home:
        return WebHomeScreenChat();
      case ScreenConstants.create:
        return BookDetailPage();
      //  case ScreenConstants.myLibrary:
      //     return WebMyLibraryScreen();
      case ScreenConstants.nslJava:
        // return NslJavaConverter();
        return PreviewScreen();

      case ScreenConstants.webMyLibrary:
        return WebMyLibraryScreen();

      case ScreenConstants.webMySolution:
        return WebSolutionsScreen();

      case ScreenConstants.webBookSolution:
        return WebBookSolutionPage();

      case ScreenConstants.webMyObject:
        return WebObjectScreen();
      case ScreenConstants.myBusinessHome:
        return WebHomeTransaction();
      case ScreenConstants.myBusinessCollections:
        return WebTransactionCollection();
      case ScreenConstants.myBusinessSolutions:
        return WebTransactionSolution();
      case ScreenConstants.myBusinessRecords:
        return WebCollectionWidgets();
      default:
        return Container(
          alignment: Alignment.center,
          child: Text('Coming Soon',
              style: TextStyle(
                fontSize: 24,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              )),
        );
    }
  }
}
