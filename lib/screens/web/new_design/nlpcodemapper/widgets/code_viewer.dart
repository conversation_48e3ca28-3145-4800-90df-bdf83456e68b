import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_highlight/flutter_highlight.dart';
import 'package:flutter_highlight/themes/github.dart';
import '../models/mapping.dart';
import '../services/highlight_service.dart';

class CodeViewer extends StatelessWidget {
  final String code;
  final String language;
  final List<Mapping> highlightedMappings;
  final Function(String, int) onLineSelected;
  final String? selectedColor; // Optional color for selected lines
  final List<dynamic>
  selectedLines; // Lines that are currently selected (can be String or LineSelection)
  final Map<String, List<int>>
  selectedLineIndices; // Map of selected lines to their indices
  final bool allowWordSelection; // Whether to allow selecting individual words
  final bool
  isInWordSelectionMode; // Track if we're in word selection mode for highlighting
  final bool isMultiSelectMode;
  final Set<int> selectedLineNumbers;
  final ScrollController?
  scrollController; // Controller for scrolling to specific lines

  const CodeViewer({
    Key? key,
    required this.code,
    required this.language,
    required this.highlightedMappings,
    required this.onLineSelected,
    this.selectedColor,
    this.selectedLines = const [],
    this.selectedLineIndices = const {}, // Initialize with empty map
    this.allowWordSelection = false, // Default to line selection
    this.isInWordSelectionMode = false, // Track if we're in word selection mode
    this.isMultiSelectMode = false,
    this.selectedLineNumbers = const {},
    this.scrollController, // Optional scroll controller
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final lines = code.split('\n');

    // Create a custom theme based on githubTheme but with transparent background
    final customTheme = Map<String, TextStyle>.from(githubTheme);
    customTheme['root'] = TextStyle(
      backgroundColor: Colors.transparent,
      color: Colors.black,
    );

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
        color: Colors.transparent,
      ),
      child: ListView.custom(
        controller: scrollController,
        childrenDelegate: SliverChildBuilderDelegate((context, index) {
          final line = lines[index];
          final mappingsForLine = _getMappingsForLine(index);

          // Get background color for the line
          Color bgColor = _getBackgroundColor(
            line,
            mappingsForLine,
            index,
            lines,
          );

          // Check if this line is highlighted
          bool isHighlighted = bgColor != Colors.transparent;

          return Container(
            // Let height be determined by content
            padding:
                isHighlighted
                    ? EdgeInsets.zero
                    : EdgeInsets.symmetric(vertical: 2),
            margin: isHighlighted ? EdgeInsets.zero : null,
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.max,
                children: [
                  // Line number
                  Container(
                    width: 40,
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    alignment: Alignment.centerRight,
                    color: Colors.grey.withOpacity(0.2),
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  // Code with syntax highlighting
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.zero,
                      child:
                          (language == 'plaintext' && allowWordSelection)
                              ? _buildSelectableCodeLine(line, context, bgColor)
                              : GestureDetector(
                                onTap: () {
                                  onLineSelected(line, index);
                                },
                                child: Container(
                                  decoration: BoxDecoration(color: bgColor),
                                  padding:
                                      isHighlighted
                                          ? EdgeInsets.zero
                                          : EdgeInsets.symmetric(vertical: 2),
                                  child: HighlightView(
                                    line,
                                    language: language,
                                    theme: customTheme,
                                    textStyle: TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 15,
                                      // Let line height be determined by content
                                      backgroundColor: Colors.transparent,
                                    ),
                                  ),
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }, childCount: lines.length),
        padding: EdgeInsets.zero,
      ),
    );
  }

  // Build a code line that allows for word selection
  Widget _buildSelectableCodeLine(
    String line,
    BuildContext context,
    Color bgColor,
  ) {
    // Check if this line is highlighted
    bool isHighlighted = bgColor != Colors.transparent;

    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(color: bgColor),
            padding: EdgeInsets.zero,
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: Colors.black,
                ),
                children: _buildWordSelectionSpans(line),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // List of common English words to filter out
  static const List<String> _commonWords = [
    'a',
    'an',
    'the',
    'and',
    'or',
    'but',
    'if',
    'then',
    'else',
    'when',
    'at',
    'by',
    'for',
    'with',
    'about',
    'against',
    'between',
    'into',
    'through',
    'during',
    'before',
    'after',
    'above',
    'below',
    'to',
    'from',
    'up',
    'down',
    'in',
    'out',
    'on',
    'off',
    'over',
    'under',
    'again',
    'further',
    'then',
    'once',
    'here',
    'there',
    'when',
    'where',
    'why',
    'how',
    'all',
    'any',
    'both',
    'each',
    'few',
    'more',
    'most',
    'other',
    'some',
    'such',
    'no',
    'nor',
    'not',
    'only',
    'own',
    'same',
    'so',
    'than',
    'too',
    'very',
    'can',
    'will',
    'just',
    'should',
    'now',
    'has',
    'have',
    'had',
    'is',
    'are',
    'was',
    'were',
    'be',
    'been',
    'being',
    'do',
    'does',
    'did',
    'doing',
    'of',
    'that',
    'this',
    'these',
    'those',
    'as',
    'it',
    'its',
    'which',
    'who',
    'whom',
    'whose',
    'what',
    'whatever',
    'whenever',
    'wherever',
    'however',
    'whichever',
    'whoever',
    'whomever',
    'would',
    'could',
    'should',
    'may',
    'might',
    'must',
    'shall',
    'need',
    'ought',
    'used',
  ];

  // Check if a word is a common word that should be filtered out
  bool _isCommonWord(String word) {
    // Clean the word by removing punctuation and converting to lowercase
    String cleanWord = word.toLowerCase().replaceAll(RegExp(r'[^\w\s]'), '');

    return _commonWords.contains(cleanWord);
  }

  // Build text spans for word selection - with filtering for common words
  List<TextSpan> _buildWordSelectionSpans(String content) {
    if (!allowWordSelection) {
      return [TextSpan(text: content)];
    }

    List<TextSpan> spans = [];

    // Split content into phrases with special handling for brackets
    List<String> parts = [];

    // First, let's handle the case with brackets
    // We'll use a more complex approach to handle nested brackets
    String remaining = content;

    while (remaining.isNotEmpty) {
      // Find the next comma
      int commaPos = remaining.indexOf(',');

      // If no more commas, add the rest as the last part
      if (commaPos == -1) {
        parts.add(remaining);
        break;
      }

      // Check if there's an opening bracket before the comma
      int openBracketPos = -1;
      String openBracket = "";
      String closeBracket = "";

      // Check for each type of bracket
      for (var bracketPair in [
        ['(', ')'],
        ['[', ']'],
        ['{', '}'],
      ]) {
        int pos = remaining.indexOf(bracketPair[0]);
        if (pos != -1 &&
            (openBracketPos == -1 || pos < openBracketPos) &&
            pos < commaPos) {
          openBracketPos = pos;
          openBracket = bracketPair[0];
          closeBracket = bracketPair[1];
        }
      }

      // If there's an opening bracket before the comma
      if (openBracketPos != -1) {
        // Find the matching closing bracket
        int bracketCount = 1;
        int closePos = openBracketPos + 1;

        while (bracketCount > 0 && closePos < remaining.length) {
          if (remaining[closePos] == openBracket) {
            bracketCount++;
          } else if (remaining[closePos] == closeBracket) {
            bracketCount--;
          }
          closePos++;
        }

        // If we found the closing bracket
        if (bracketCount == 0) {
          // Check if there's a comma after the closing bracket
          int nextCommaPos = remaining.indexOf(',', closePos);

          if (nextCommaPos != -1) {
            // Add everything up to and including the comma
            parts.add(remaining.substring(0, nextCommaPos + 1));
            remaining = remaining.substring(nextCommaPos + 1);
          } else {
            // No comma after the closing bracket, add the rest
            parts.add(remaining);
            break;
          }
        } else {
          // No matching closing bracket, treat as normal
          parts.add(remaining.substring(0, commaPos + 1));
          remaining = remaining.substring(commaPos + 1);
        }
      } else {
        // No opening bracket before the comma, treat as normal
        parts.add(remaining.substring(0, commaPos + 1));
        remaining = remaining.substring(commaPos + 1);
      }
    }

    // If we didn't find any parts (which shouldn't happen), fall back to simple splitting
    if (parts.isEmpty) {
      RegExp phraseRegex = RegExp(r'[^,]+,|[^,]+$');
      Iterable<RegExpMatch> matches = phraseRegex.allMatches(content);
      for (var match in matches) {
        parts.add(match.group(0)!);
      }
    }

    // Process each part to create spans
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];

      // Skip empty parts
      if (part.isEmpty) continue;

      // Check if this part is just a common word
      String trimmedPart = part.trim();
      if (trimmedPart.endsWith(",")) {
        trimmedPart = trimmedPart.substring(0, trimmedPart.length - 1).trim();
      }

      // If it's a standalone common word, display it as non-selectable
      if (!trimmedPart.contains(" ") &&
          !trimmedPart.contains("(") &&
          !trimmedPart.contains("[") &&
          !trimmedPart.contains("{") &&
          _isCommonWord(trimmedPart)) {
        spans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              color: Colors.grey.shade500, // Grey out common words
              fontStyle: FontStyle.italic,
            ),
          ),
        );
        continue;
      }

      // Check if this part contains brackets - if so, make the entire part selectable
      bool containsBrackets =
          trimmedPart.contains('(') ||
          trimmedPart.contains('[') ||
          trimmedPart.contains('{');

      if (containsBrackets) {
        // Check if this part is selected
        bool isSelected = false;
        if (language == 'plaintext' &&
            isInWordSelectionMode &&
            selectedLines.isNotEmpty) {
          for (var selected in selectedLines) {
            if (selected is String && trimmedPart == selected.trim()) {
              isSelected = true;
              break;
            }
          }
        }

        spans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              backgroundColor:
                  isSelected && selectedColor != null
                      ? HighlightService.getColorFromHex(
                        selectedColor ?? "#3498DB",
                      ).withOpacity(0.3)
                      : Colors.transparent,
              // Add a special style for phrases with brackets
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            recognizer:
                TapGestureRecognizer()
                  ..onTap = () {
                    // Handle phrase selection
                    onLineSelected(trimmedPart, -1);
                  },
          ),
        );

        continue; // Skip the rest of the processing for this part
      }

      // For phrases that contain spaces, process all words to find common words
      if (trimmedPart.contains(" ")) {
        // Split the phrase into words
        List<String> words = trimmedPart.split(" ");

        // Create a list to track which words are common
        List<bool> isCommonWord = List.generate(
          words.length,
          (index) => _isCommonWord(words[index]),
        );

        // If any word is common, we need to create multiple spans
        if (isCommonWord.contains(true)) {
          int currentPos = 0;
          String currentText = part;

          for (int j = 0; j < words.length; j++) {
            String word = words[j];
            int wordPos = currentText.indexOf(word, currentPos);

            if (wordPos >= 0) {
              // If this is a common word, make it non-selectable
              if (isCommonWord[j]) {
                // Add any text before this word
                if (wordPos > currentPos) {
                  String beforeText = currentText.substring(
                    currentPos,
                    wordPos,
                  );
                  spans.add(
                    TextSpan(
                      text: beforeText,
                      style: TextStyle(color: Colors.black),
                    ),
                  );
                }

                // Add the common word as non-selectable
                spans.add(
                  TextSpan(
                    text: word + (j < words.length - 1 ? " " : ""),
                    style: TextStyle(
                      color: Colors.grey.shade500, // Grey out common words
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                );

                // Update current position
                currentPos =
                    wordPos +
                    word.length +
                    (j < words.length - 1 ? 1 : 0); // +1 for space
              }
              // If this is the last word or the next word is common, make this word selectable
              else if (j == words.length - 1 || isCommonWord[j + 1]) {
                // Add any text before this word
                if (wordPos > currentPos) {
                  String beforeText = currentText.substring(
                    currentPos,
                    wordPos,
                  );
                  spans.add(
                    TextSpan(
                      text: beforeText,
                      style: TextStyle(color: Colors.black),
                    ),
                  );
                }

                // Add the word as selectable
                String selectableText =
                    word + (j < words.length - 1 ? " " : "");
                bool isSelected = false;
                if (language == 'plaintext' &&
                    isInWordSelectionMode &&
                    selectedLines.isNotEmpty) {
                  for (var selected in selectedLines) {
                    if (selected is String && word.trim() == selected.trim()) {
                      isSelected = true;
                      break;
                    }
                  }
                }

                spans.add(
                  TextSpan(
                    text: selectableText,
                    style: TextStyle(
                      backgroundColor:
                          isSelected && selectedColor != null
                              ? HighlightService.getColorFromHex(
                                selectedColor ?? "#3498DB",
                              ).withOpacity(0.3)
                              : Colors.transparent,
                      color: Colors.black,
                    ),
                    recognizer:
                        TapGestureRecognizer()
                          ..onTap = () {
                            onLineSelected(word.trim(), -1);
                          },
                  ),
                );

                // Update current position
                currentPos =
                    wordPos +
                    word.length +
                    (j < words.length - 1 ? 1 : 0); // +1 for space
              }
              // Otherwise, continue to the next word
              else {
                continue;
              }
            }
          }

          // Add any remaining text
          if (currentPos < currentText.length) {
            spans.add(
              TextSpan(
                text: currentText.substring(currentPos),
                style: TextStyle(color: Colors.black),
              ),
            );
          }

          continue; // Skip the default span creation below
        }
      }

      // Check if this part is selected
      bool isSelected = false;
      if (language == 'plaintext' &&
          isInWordSelectionMode &&
          selectedLines.isNotEmpty) {
        for (var selected in selectedLines) {
          if (selected is String && trimmedPart == selected.trim()) {
            isSelected = true;
            break;
          }
        }
      }

      // Create a text span for this part (default case)
      spans.add(
        TextSpan(
          text: part,
          style: TextStyle(
            backgroundColor:
                isSelected && selectedColor != null
                    ? HighlightService.getColorFromHex(
                      selectedColor ?? "#3498DB",
                    ).withOpacity(0.3)
                    : Colors.transparent,
            color: Colors.black,
            fontWeight: FontWeight.normal,
          ),
          recognizer:
              TapGestureRecognizer()
                ..onTap = () {
                  // Handle phrase selection
                  onLineSelected(trimmedPart, -1);
                },
        ),
      );
    }

    return spans;
  }

  // Get the background color for a line based on mappings and selection
  Color _getBackgroundColor(
    String line,
    List<Mapping> mappingsForLine,
    int lineIndex,
    List<String> allLines,
  ) {
    const defaultColor = "#3498DB"; // Default blue color

    // For NLP in word selection mode, we only want to highlight the line containing the selected word
    if (language == 'plaintext' && isInWordSelectionMode) {
      // Check if this line contains the selected word
      bool lineContainsSelectedWord = false;
      if (selectedLines.isNotEmpty) {
        for (var selectedWord in selectedLines) {
          if (selectedWord is String) {
            // Extract just the word part if it contains position information
            String wordToCheck = selectedWord;
            if (selectedWord.contains(":")) {
              wordToCheck = selectedWord.split(":")[0];
            }

            // Check if this line contains the selected word as a whole word
            if (line.contains(wordToCheck)) {
              lineContainsSelectedWord = true;
              break;
            }
          }
        }
      }

      // If this line contains the selected word, return a very light background color
      // Otherwise return transparent
      if (lineContainsSelectedWord) {
        return HighlightService.getColorFromHex(
          selectedColor ?? defaultColor,
        ).withOpacity(0.1);
      } else {
        return Colors.transparent;
      }
    }

    // For other cases (Java, SQL, or NLP in line selection mode)
    // First check if this line is in the selected lines using a more robust comparison
    // and also check if the line index matches
    bool isLineSelected = false;

    for (var selectedLine in selectedLines) {
      if (selectedLine is String) {
        // Compare trimmed versions to handle whitespace differences
        if (line.trim() == selectedLine.trim()) {
          // Check if this is the exact line that was clicked by comparing line indices
          if (selectedLineIndices.containsKey(selectedLine) &&
              selectedLineIndices[selectedLine]?.contains(lineIndex) == true) {
            isLineSelected = true;
            break;
          }
        }
      } else {
        // Handle LineSelection objects
        try {
          final lineSelection = selectedLine;
          if (line.trim() == lineSelection.content.trim() &&
              lineIndex == lineSelection.lineIndex) {
            isLineSelected = true;
            break;
          }
        } catch (e) {
          print('Error handling selected line: $e');
        }
      }
    }

    // If the line is selected, return the selected color
    if (isLineSelected) {
      return HighlightService.getColorFromHex(
        selectedColor ?? defaultColor,
      ).withOpacity(0.3);
    }

    // Check if this line is part of a block of related lines
    bool isPartOfBlock = false;
    if (mappingsForLine.isNotEmpty) {
      // Get the mapping for this line
      final mapping = mappingsForLine.first;

      // Check if this line is part of a Java or SQL block
      if (language == 'java') {
        isPartOfBlock = mapping.javaLines.any(
          (lineSelection) => lineSelection.content == line,
        );
      } else if (language == 'sql') {
        isPartOfBlock = mapping.sqlLines.any(
          (lineSelection) => lineSelection.content == line,
        );
      }
    }

    // If the line is part of a block, return the mapping color
    if (isPartOfBlock && mappingsForLine.isNotEmpty) {
      return HighlightService.getColorFromHex(
        mappingsForLine.first.color,
      ).withOpacity(0.1);
    }

    return Colors.transparent;
  }

  List<Mapping> _getMappingsForLine(int lineIndex) {
    final currentLine = HighlightService.getLineByNumber(code, lineIndex);

    List<Mapping> result = [];

    for (final mapping in highlightedMappings) {
      // Check if the current line matches any Java or SQL lines in the mapping
      if (language == 'java') {
        if (mapping.javaLines.any(
          (lineSelection) =>
              currentLine.trim() == lineSelection.content.trim() &&
              lineIndex == lineSelection.lineIndex,
        )) {
          result.add(mapping);
        }
      } else if (language == 'sql') {
        if (mapping.sqlLines.any(
          (lineSelection) =>
              currentLine.trim() == lineSelection.content.trim() &&
              lineIndex == lineSelection.lineIndex,
        )) {
          result.add(mapping);
        }
      } else if (language == 'plaintext') {
        // For NLP
        if (currentLine.trim() == mapping.nlpLine.trim()) {
          result.add(mapping);
        }
      }
    }

    return result;
  }
}
