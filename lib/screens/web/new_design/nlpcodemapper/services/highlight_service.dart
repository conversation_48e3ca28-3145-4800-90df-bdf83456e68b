import 'dart:ui';
import '../models/mapping.dart';

class HighlightService {
  // Color mapping for different mapping types
  static Color getColorForMappingType(MappingType type) {
    switch (type) {
      case MappingType.entity:
        return Color(0xFFAED581); // Light green
      case MappingType.attribute:
        return Color(0xFF81D4FA); // Light blue
      case MappingType.relation:
        return Color(0xFFFFD54F); // Amber
      case MappingType.validation:
        return Color(0xFFFF8A65); // Light orange
      case MappingType.other:
      default:
        return Color(0xFFE1BEE7); // Light purple
    }
  }
  
  // Convert hex color string to Color object
  static Color getColorFromHex(String hexColor) {
    // Remove # if present
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    
    // Add FF for opacity if needed
    if (hexColor.length == 6) {
      hexColor = "FF" + hexColor;
    }
    
    // Parse the hex value
    try {
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Return a default color if parsing fails
      return Color(0xFF3498DB); // Default blue
    }
  }

  // Find the line number for a specific text in a code block
  static List<int> findLineNumbers(String codeBlock, List<String> linesToFind) {
    final List<int> lineNumbers = [];
    final List<String> lines = codeBlock.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final String line = lines[i].trim();
      if (linesToFind.any((lineToFind) => line.contains(lineToFind.trim()))) {
        lineNumbers.add(i);
      }
    }

    return lineNumbers;
  }

  // Extract a specific line from text by line number
  static String getLineByNumber(String text, int lineNumber) {
    final lines = text.split('\n');
    if (lineNumber >= 0 && lineNumber < lines.length) {
      return lines[lineNumber];
    }
    return '';
  }

  // Get the text range for a specific line
  static Map<String, int> getLineRange(String text, int lineNumber) {
    final lines = text.split('\n');
    int startIndex = 0;

    for (int i = 0; i < lineNumber; i++) {
      if (i < lines.length) {
        startIndex += lines[i].length + 1; // +1 for newline character
      }
    }

    int endIndex = startIndex;
    if (lineNumber < lines.length) {
      endIndex += lines[lineNumber].length;
    }

    return {
      'start': startIndex,
      'end': endIndex,
    };
  }
}
