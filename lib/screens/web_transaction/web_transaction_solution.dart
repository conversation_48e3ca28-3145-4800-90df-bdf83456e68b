import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:carousel_slider/carousel_slider.dart';

/// Model class for collection data
class CollectionModel {
  final String title;
  final CustomImage image;
  final CustomImage? icon; // Icon can be null if not provided
  final String status; // Status for the button (e.g., "pending", "start")

  CollectionModel({
    required this.title,
    required this.image,
    this.icon,
    this.status = 'Start', // Default status is 'Start'
  });

  factory CollectionModel.fromJson(Map<String, dynamic> json) {
    return CollectionModel(
      title: json['title'] as String,
      image: json['image'] is String
          ? CustomImage.asset(json['image'] as String)
          : CustomImage.fromJson(json['image'] as Map<String, dynamic>),
      icon: json['icon'] == null
          ? null
          : json['icon'] is String
              ? CustomImage.asset(json['icon'] as String)
              : CustomImage.fromJson(json['icon'] as Map<String, dynamic>),
      status: json['status'] as String? ??
          'Start', // Default to 'Start' if not provided
    );
  }
}

class WebTransactionSolution extends StatefulWidget {
  final String? jsonDataPath;

  const WebTransactionSolution({
    super.key,
    this.jsonDataPath = 'assets/data/collections.json',
  });

  @override
  State<WebTransactionSolution> createState() => _WebTransactionSolutionState();
}

class _WebTransactionSolutionState extends State<WebTransactionSolution> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = true;
  List<CollectionModel> _collections = [];
  List<CollectionModel> _filteredCollections = [];

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Pagination
  static const int itemsPerPage = 14;
  int _currentCollectionPage = 1;
  int _totalCollectionPages = 1;

  // Sample banner images - using a placeholder image for now
  final List<String> bannerImages = [
    'assets/images/my_business/collections/my_business_carousel_one.jpg',
    'assets/images/my_business/collections/my_business_carousel_two.jpg',
    'assets/images/my_business/collections/my_business_carousel_three.jpg',
  ];
  final CarouselSliderController carouselController =
      CarouselSliderController();
  bool isHovered = false;

  @override
  void initState() {
    super.initState();
    // Clear asset cache before loading data
    rootBundle.evict(widget.jsonDataPath ?? 'assets/data/collections.json');
    _loadCollectionsData();

    // Initialize filtered collections
    _filteredCollections = List.from(_collections);

    // Initialize pagination
    _currentCollectionPage = 1;
    _updateTotalPages();

    // Add listener to search controller
    _searchController.addListener(_filterCollections);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterCollections);
    _searchController.dispose();
    super.dispose();
  }

  // Filter collections based on search text and update pagination
  void _filterCollections() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredCollections = List.from(_collections);
      } else {
        _filteredCollections = _collections
            .where((collection) =>
                collection.title.toLowerCase().contains(searchText))
            .toList();
      }

      // Reset to first page when filtering
      _currentCollectionPage = 1;
      _updateTotalPages();
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  // Update total pages based on filtered collections
  void _updateTotalPages() {
    _totalCollectionPages = (_filteredCollections.length / itemsPerPage).ceil();
    if (_totalCollectionPages < 1) _totalCollectionPages = 1;
  }

  // Navigate to previous page
  void _previousPage() {
    if (_currentCollectionPage > 1) {
      setState(() {
        _currentCollectionPage--;
      });
    }
  }

  // Navigate to next page
  void _nextPage() {
    if (_currentCollectionPage < _totalCollectionPages) {
      setState(() {
        _currentCollectionPage++;
      });
    }
  }

  // Get current page items
  List<CollectionModel> _getCurrentPageItems() {
    if (_filteredCollections.isEmpty) return [];

    final startIndex = (_currentCollectionPage - 1) * itemsPerPage;
    final endIndex = startIndex + itemsPerPage;

    if (startIndex >= _filteredCollections.length) return [];

    return _filteredCollections.sublist(
        startIndex,
        endIndex > _filteredCollections.length
            ? _filteredCollections.length
            : endIndex);
  }

  /// Load collections data from JSON file
  Future<void> _loadCollectionsData() async {
    try {
      // For development/testing, use hardcoded data if JSON file loading fails
      final List<CollectionModel> fallbackData = [
        CollectionModel(
          title: "Apply Leave",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_apply.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "Employee Details",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_employee_details.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "My completed Module",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_module.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "My Attendance",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_attendance.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "Organisation Policy Details",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_organisation_details.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "Product Details",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_product_details.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "Payment Refund",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_payment_refund.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "My Orders",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_orders.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "My Wallet",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_wallet.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "Return Policy",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_return_policy.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "Home Consumer Product Booking",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_home_consumer.jpg"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "Users tasks Favorites",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_user_tasks.jpg"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
      ];

      if (widget.jsonDataPath == null) {
        setState(() {
          _collections = fallbackData;
          _isLoading = false;
        });
        return;
      }

      // Add more test data to demonstrate pagination
      final List<CollectionModel> additionalData = [
        CollectionModel(
          title: "Payment History",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_payment_history.jpg"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "My Profile",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_profile.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "Sales Analytics",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_profile.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "IT Support",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_profile.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
        CollectionModel(
          title: "Product Development",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_profile.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Start",
        ),
        CollectionModel(
          title: "Business Intelligence",
          image: CustomImage.asset(
              "assets/images/my_business/solutions/solution_my_profile.png"),
          icon: CustomImage.asset("assets/images/my_business/right_arrow.svg"),
          status: "Pending",
        ),
      ];

      // Combine fallback data with additional data
      fallbackData.addAll(additionalData);

      // Use combined data
      setState(() {
        _collections = fallbackData;
        _filteredCollections = List.from(_collections);
        _updateTotalPages();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _collections = [];
        _filteredCollections = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Row(
            children: [
              Expanded(child: SizedBox()),
              Expanded(
                flex: 9,
                child: Column(
                  children: [
                    // Banner Slider
                    SizedBox(
                      height: AppSpacing.xs,
                    ),
                    MouseRegion(
                      onEnter: (_) {
                        setState(() {
                          isHovered = true;
                        });
                      },
                      onExit: (_) {
                        setState(() {
                          isHovered = false;
                        });
                      },
                      child: Stack(
                        children: [
                          CarouselSlider.builder(
                            itemCount: bannerImages.length,
                            itemBuilder: (context, index, realIndex) {
                              return Image.asset(
                                bannerImages[index],
                                fit: BoxFit.cover,
                                width: double.infinity,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: double.infinity,
                                    height: 180,
                                    color: Colors.grey[200],
                                    child: Center(
                                      child: Icon(
                                        Icons.broken_image,
                                        color: Colors.grey[400],
                                        size: 48,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            options: CarouselOptions(
                              height: MediaQuery.of(context).size.height / 3.5,
                              viewportFraction: 1.0,
                              autoPlay: true,
                              autoPlayInterval: Duration(seconds: 5),
                              onPageChanged: (index, reason) {
                                setState(() {
                                  _currentPage = index;
                                });
                              },
                            ),
                            carouselController: carouselController,
                          ),

                          // Left arrow image (only on hover)
                          if (isHovered)
                            Positioned(
                              left: 8,
                              top: 0,
                              bottom: 0,
                              child: Center(
                                child: InkWell(
                                  onTap: () {
                                    carouselController.previousPage(
                                      duration: Duration(milliseconds: 300),
                                      curve: Curves.ease,
                                    );
                                  },
                                  child: CustomImage.asset(
                                    "assets/images/my_business/collections/left_carousel.svg",
                                  ).toWidget(),
                                ),
                              ),
                            ),

                          // Right arrow image (only on hover)
                          if (isHovered)
                            Positioned(
                              right: 8,
                              top: 0,
                              bottom: 0,
                              child: Center(
                                child: InkWell(
                                  onTap: () {
                                    carouselController.nextPage(
                                      duration: Duration(milliseconds: 300),
                                      curve: Curves.ease,
                                    );
                                  },
                                  child: CustomImage.asset(
                                    "assets/images/my_business/collections/right_carousel.svg",
                                  ).toWidget(),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // Dot Indicator
                    Padding(
                      padding:
                          const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          bannerImages.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.xxs),
                            width: _currentPage == index ? 20 : 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _currentPage == index
                                  ? Colors.blue
                                  : Colors.grey,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // Collections title & search icon/bar
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('myBusinessSolutions.solutions'),
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                        _showSearchBar
                            ? SearchBarWidget(
                                controller: _searchController,
                                onClose: _toggleSearchBar,
                              )
                            : GestureDetector(
                                onTap: _toggleSearchBar,
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Container(
                                    width: 24,
                                    height: 24,
                                    margin: const EdgeInsets.all(8),
                                    child: CustomImage.asset(
                                      'assets/images/my_business/search_collection.svg',
                                      width: 24,
                                      height: 24,
                                      fit: BoxFit.contain,
                                    ).toWidget(),
                                  ),
                                ),
                              ),
                      ],
                    ),
                    SizedBox(height: AppSpacing.xs),

                    // Loading indicator or grid
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : LayoutBuilder(builder: (context, constraints) {
                            // Calculate columns based on available width
                            int crossAxisCount = 2;

                            // Adjust number of columns based on screen width
                            if (constraints.maxWidth >= 1920) {
                              crossAxisCount = 7;
                            } else if (constraints.maxWidth >= 1600) {
                              crossAxisCount = 7;
                            } else if (constraints.maxWidth >= 1100) {
                              crossAxisCount = 7;
                            } else if (constraints.maxWidth >= 800) {
                              crossAxisCount = 7;
                            } else if (constraints.maxWidth >= 600) {
                              crossAxisCount = 2;
                            }

                            // Calculate grid height based on number of rows needed
                            final int itemCount = _getCurrentPageItems().length;
                            final int rowCount =
                                (itemCount / crossAxisCount).ceil();
                            final double cardHeight =
                                140; // Height of each card including margin

                            // Adjust height based on content and screen size
                            final double gridHeight = constraints.maxWidth >=
                                    1600
                                ? min(rowCount * cardHeight,
                                    600) // Cap at 600px for large screens
                                : constraints.maxWidth >= 1100
                                    ? min(rowCount * cardHeight,
                                        500) // Cap at 500px for medium screens
                                    : min(rowCount * cardHeight,
                                        450); // Cap at 450px for small screens

                            return GridView.builder(
                              shrinkWrap: true,
                              physics:
                                  const AlwaysScrollableScrollPhysics(), // Make it scrollable
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 16,
                                childAspectRatio:
                                    0.75, // Slightly taller than wide to match the screenshot
                              ),
                              itemCount: _getCurrentPageItems().length,
                              itemBuilder: (context, index) {
                                final collection =
                                    _getCurrentPageItems()[index];
                                return CollectionCard(
                                  title: collection.title,
                                  image: collection.image,
                                  icon: collection.icon,
                                  status: collection.status,
                                );
                              },
                            );
                          }),
                    // Pagination controls
                    Container(
                      margin:
                          const EdgeInsets.symmetric(vertical: AppSpacing.md),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Page indicator
                          // Text(
                          //   'Page $_currentCollectionPage of $_totalCollectionPages',
                          //   style: const TextStyle(
                          //     fontSize: 14,
                          //     color: Colors.grey,
                          //   ),
                          // ),

                          // Navigation buttons
                          Row(
                            children: [
                              // Previous button
                              _HoverPaginationButton(
                                icon: const Icon(Icons.chevron_left, size: 20),
                                onPressed: _currentCollectionPage > 1
                                    ? () => _previousPage()
                                    : null,
                              ),
                              const SizedBox(width: 8),
                              // Next button
                              _HoverPaginationButton(
                                icon: const Icon(Icons.chevron_right, size: 20),
                                onPressed: _currentCollectionPage <
                                        _totalCollectionPages
                                    ? () => _nextPage()
                                    : null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(child: SizedBox()),
            ],
          ),
        ),
      ),
    );
  }
}

class CollectionCard extends StatefulWidget {
  final String title;
  final CustomImage image;
  final CustomImage? icon;
  final String status;

  const CollectionCard({
    super.key,
    required this.title,
    required this.image,
    this.icon,
    required this.status,
  });

  @override
  _CollectionCardState createState() => _CollectionCardState();
}

class _CollectionCardState extends State<CollectionCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Card(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.xs),
          side: BorderSide(
            color: isHovered ? Color(0xff0058FF) : Color(0xffD0D0D0),
            width: isHovered ? 1 : 0.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 3,
                child: ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(8)),
                  child: widget.image.toWidget(
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              ),
              SizedBox(
                height: AppSpacing.xs,
              ),
              Expanded(
                flex: 1,
                child: Text(
                  widget.title,
                  style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'TiemposText',
                      color: Colors.black),
                  textAlign: TextAlign.left,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search icon
          // Padding(
          //   padding: const EdgeInsets.only(left: 8.0),
          //   child: Icon(Icons.search, size: 18, color: Colors.grey.shade600),
          // ),

          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 14,
                    fontFamily: 'TiemposText'),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
