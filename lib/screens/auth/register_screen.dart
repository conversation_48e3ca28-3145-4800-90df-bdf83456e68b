import 'dart:io';
import 'package:flutter/material.dart';
import 'package:nsl/utils/constants.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../services/image_service.dart';
import '../../ui_components/inputs/app_text_field.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../utils/logger.dart';
import '../../utils/navigation_service.dart';
import '../../utils/validators.dart';
import '../../widgets/auth/auth_button.dart';
import '../../widgets/auth/auth_link.dart';
import '../../widgets/auth/password_field.dart';
import '../../widgets/auth/validated_text_field.dart';
import 'base_auth_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _organizationController = TextEditingController(text: 'org_it');
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final _imageService = ImageService();

  String _selectedRole = 'User';
  File? _profileImage;
  String? _base64Image;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _mobileController.dispose();
    _organizationController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _selectProfileImage() async {
    final File? image = await _imageService.showImagePickerDialog(context);
    if (image != null) {
      setState(() {
        _profileImage = image;
      });

      // Convert to base64 for API
      _base64Image = await _imageService.imageToBase64(image);
    }
  }

  Future<void> _register() async {
    // Check if profile picture is selected
    // if (_profileImage == null) {
    //   _showErrorDialog('Please select a profile picture');
    //   return;
    // }

    // Validate form
    if (_formKey.currentState?.validate() ?? false) {
      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        final firstName = _firstNameController.text.trim();
        final lastName = _lastNameController.text.trim();
        final email = _emailController.text.trim();
        final username = _usernameController.text.trim();
        final mobile = _mobileController.text.trim();
        final password = _passwordController.text;
        final organization = _organizationController.text.trim();

        // Attempt registration
        final success = await authProvider.register(
          name: '$firstName $lastName',
          username: username,
          email: email,
          mobile: mobile,
          password: password,
          role: _selectedRole,
          organization: organization,
          profilePicture: _base64Image,
        );

        if (success) {
          Logger.info('Registration successful, navigating to login screen');

          // Show success dialog and then navigate to login screen
          _showSuccessDialog();
        } else {
          Logger.info('Registration failed');

          // Show error dialog
          if (authProvider.error != null) {
            _showErrorDialog(authProvider.error!);
          } else {
            _showErrorDialog('Registration failed. Please try again.');
          }
        }
      } catch (e) {
        Logger.error('Error during registration process: $e');
        // Show error dialog for exceptions
        _showErrorDialog(
            'An error occurred during registration. Please try again.');
      }
    } else {
      Logger.info('Form validation failed');
    }
  }

  // Show error dialog
  void _showErrorDialog(String errorMessage) {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      // if (!mounted) return;

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.error_outline, color: AppTheme.errorColor),
                const SizedBox(width: 8),
                Text(context.tr('common.error')),
              ],
            ),
            content: Text(errorMessage),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(context.tr('common.close')),
              ),
            ],
          );
        },
      );
    });
  }

  // Show success dialog and navigate to login screen
  void _showSuccessDialog() {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      // if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false, // User must tap button to close dialog
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.check_circle_outline,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Expanded(child: Text(context.tr('common.success'))),
              ],
            ),
            content: Text(context.tr('auth.registrationSuccess')),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close the dialog
                  // Navigate to login screen
                  NavigationService.navigateToLogin();
                  Logger.info('Navigation to login screen completed');
                },
                child: Text(context.tr('auth.login')),
              ),
            ],
          );
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        return SafeArea(
          child: BaseAuthScreen(
            title: context.tr('auth.signUp'),
            subtitle: context.tr('auth.pleaseSignIn'),
            isLoading: authProvider.isLoading,
            errorMessage: authProvider.error,
            form: _buildRegisterForm(context, authProvider),
          ),
        );
      },
    );
  }

  Widget _buildRegisterForm(BuildContext context, AuthProvider authProvider) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Profile Picture
          // Column(
          //   children: [
          //     Center(
          //       child: GestureDetector(
          //         onTap: !authProvider.isLoading ? _selectProfileImage : null,
          //         child: Stack(
          //           children: [
          //             Container(
          //               width: 120,
          //               height: 120,
          //               decoration: BoxDecoration(
          //                 color: Theme.of(context)
          //                     .colorScheme
          //                     .primary
          //                     .withAlpha(25),
          //                 shape: BoxShape.circle,
          //                 border: Border.all(
          //                   color: Theme.of(context)
          //                       .colorScheme
          //                       .primary
          //                       .withAlpha(50),
          //                   width: 2,
          //                 ),
          //               ),
          //               child: ClipOval(
          //                 child: _profileImage != null
          //                     ? Image.file(
          //                         _profileImage!,
          //                         fit: BoxFit.cover,
          //                         width: 120,
          //                         height: 120,
          //                       )
          //                     : Icon(
          //                         Icons.person,
          //                         size: 60,
          //                         color: Theme.of(context).colorScheme.primary,
          //                       ),
          //               ),
          //             ),
          //             Positioned(
          //               bottom: 0,
          //               right: 0,
          //               child: Container(
          //                 decoration: BoxDecoration(
          //                   color: Theme.of(context).colorScheme.primary,
          //                   shape: BoxShape.circle,
          //                 ),
          //                 padding: const EdgeInsets.all(8),
          //                 child: Icon(
          //                   Icons.camera_alt,
          //                   color: Theme.of(context).colorScheme.onPrimary,
          //                   size: 20,
          //                 ),
          //               ),
          //             ),
          //           ],
          //         ),
          //       ),
          //     ),
          //     if (_profileImage != null)
          //       Padding(
          //         padding: const EdgeInsets.only(top: 8.0),
          //         child: Row(
          //           mainAxisAlignment: MainAxisAlignment.center,
          //           children: [
          //             TextButton.icon(
          //               icon: const Icon(Icons.refresh),
          //               label: const Text('Change'),
          //               onPressed: !authProvider.isLoading
          //                   ? _selectProfileImage
          //                   : null,
          //             ),
          //             TextButton.icon(
          //               icon: const Icon(Icons.delete),
          //               label: const Text('Remove'),
          //               onPressed: !authProvider.isLoading
          //                   ? () {
          //                       setState(() {
          //                         _profileImage = null;
          //                         _base64Image = null;
          //                       });
          //                     }
          //                   : null,
          //             ),
          //           ],
          //         ),
          //       ),
          //   ],
          // ),
          const SizedBox(height: AppTheme.spacingM),

          // First Name Field
          ValidatedTextField(
            controller: _firstNameController,
            label: context.tr('auth.firstName'),
            placeholder: 'Enter your first name',
            type: AppTextFieldType.text,
            textInputAction: TextInputAction.next,
            prefix: const Icon(Icons.person_outline),
            enabled: !authProvider.isLoading,
            validator: (value) =>
                Validators.validateRequired(value, 'First name'),
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Last Name Field
          ValidatedTextField(
            controller: _lastNameController,
            label: context.tr('auth.lastName'),
            placeholder: 'Enter your last name',
            type: AppTextFieldType.text,
            textInputAction: TextInputAction.next,
            prefix: const Icon(Icons.account_circle_outlined),
            enabled: !authProvider.isLoading,
            validator: (value) =>
                Validators.validateRequired(value, 'Last name'),
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Email Field
          ValidatedTextField(
            controller: _emailController,
            label: context.tr('auth.email'),
            placeholder: 'Enter your email',
            type: AppTextFieldType.email,
            textInputAction: TextInputAction.next,
            prefix: const Icon(Icons.email_outlined),
            enabled: !authProvider.isLoading,
            validator: Validators.validateEmail,
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Username Field
          ValidatedTextField(
            controller: _usernameController,
            label: context.tr('auth.username'),
            placeholder: 'Choose a username',
            type: AppTextFieldType.text,
            textInputAction: TextInputAction.next,
            prefix: const Icon(Icons.account_box_outlined),
            enabled: !authProvider.isLoading,
            validator: Validators.validateUsername,
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Mobile Number Field
          // ValidatedTextField(
          //   controller: _mobileController,
          //   label: 'Mobile Number',
          //   placeholder: 'Enter your 10-digit mobile number',
          //   type: AppTextFieldType.number,
          //   textInputAction: TextInputAction.next,
          //   prefix: const Icon(Icons.phone_outlined),
          //   enabled: !authProvider.isLoading,
          //   validator: Validators.validateMobile,
          //   inputFormatters: [
          //     FilteringTextInputFormatter.digitsOnly,
          //     LengthLimitingTextInputFormatter(10),
          //   ],
          //   isRequired: true,
          // ),
          // const SizedBox(height: AppTheme.spacingM),

          // Organization Field
          ValidatedTextField(
            controller: _organizationController,
            label: context.tr('auth.organization'),
            placeholder: 'Enter your organization name',
            type: AppTextFieldType.text,
            textInputAction: TextInputAction.next,
            prefix: const Icon(Icons.business_outlined),
            enabled: false,
            //  !authProvider.isLoading,
            validator: Validators.validateOrganization,
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Role Dropdown
          DropdownButtonFormField<String>(
            value: _selectedRole,
            decoration: InputDecoration(
              labelText: context.tr('auth.role'),
              prefixIcon: const Icon(Icons.assignment_ind_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            items: AppConstants.getRoles()
                .cast<String>()
                .map<DropdownMenuItem<String>>((String role) => DropdownMenuItem<String>(
                      value: role,
                      child: Text(role),
                    ))
                .toList(),
            validator: Validators.validateRole,
            onChanged: authProvider.isLoading
                ? null
                : (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedRole = value;
                      });
                    }
                  },
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Password Field
          PasswordField(
            controller: _passwordController,
            label: context.tr('auth.password'),
            placeholder: 'Enter your password',
            textInputAction: TextInputAction.next,
            enabled: !authProvider.isLoading,
            validator: Validators.validatePassword,
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Confirm Password Field
          PasswordField(
            controller: _confirmPasswordController,
            label: context.tr('auth.confirmPassword'),
            placeholder: 'Confirm your password',
            textInputAction: TextInputAction.done,
            enabled: !authProvider.isLoading,
            validator: (value) => Validators.validatePasswordMatch(
              value,
              _passwordController.text,
            ),
            isRequired: true,
          ),
          const SizedBox(height: AppTheme.spacingL),

          // Register Button
          AuthButton(
            text: context.tr('auth.register'),
            onPressed: _register,
            isLoading: authProvider.isLoading,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Login Link
          AuthLink(
            text: "${context.tr('auth.alreadyRegistered')} ",
            linkText: context.tr('auth.login'),
            onPressed: () {
              // Navigate back to login screen using NavigationService
              NavigationService.pop();
            },
            isDisabled: authProvider.isLoading,
          ),
        ],
      ),
    );
  }
}
