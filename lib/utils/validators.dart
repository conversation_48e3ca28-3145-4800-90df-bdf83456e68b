import 'constants.dart';

/// A utility class for form validation
class Validators {
  /// Validates if a string is not empty
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validates email format
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!AppConstants.emailPattern.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Validates mobile number format (10 digits)
  static String? validateMobile(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Mobile number is required';
    }
    if (!AppConstants.mobilePattern.hasMatch(value)) {
      return 'Please enter a valid 10-digit mobile number';
    }
    return null;
  }

  /// Validates password strength
  static String? validatePassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Password is required';
    }
    if (value.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }
    return null;
  }

  /// Validates if passwords match
  static String? validatePasswordMatch(String? value, String? password) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }

  /// Validates username format
  static String? validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Username is required';
    }
    if (value.trim().length < 3) {
      return 'Username must be at least 3 characters';
    }
    if (value.trim().length > 30) {
      return 'Username must be less than 30 characters';
    }
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
      return 'Username can only contain letters, numbers, and underscores';
    }
    return null;
  }

  /// Validates role selection
  static String? validateRole(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Role is required';
    }
    final validRoles = AppConstants.getRoles();
    if (!validRoles.contains(value)) {
      return 'Please select a valid role';
    }
    return null;
  }

  /// Validates organization name
  static String? validateOrganization(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Organization name is required';
    }
    if (value.trim().length < 2) {
      return 'Organization name must be at least 2 characters';
    }
    if (value.trim().length > 100) {
      return 'Organization name must be less than 100 characters';
    }
    return null;
  }
}
