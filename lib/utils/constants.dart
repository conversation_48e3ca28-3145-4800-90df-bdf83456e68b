import 'package:package_info_plus/package_info_plus.dart';

/// Application-wide constants
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // App Information
  static const String appName = 'NSL';
  static const String defaultAppVersion = '1.0.0';

  /// Get the app version from pubspec.yaml
  static Future<String> getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return defaultAppVersion;
    }
  }

  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'auth_user';
  static const String rememberMeKey = 'auth_remember_me';
  static const String emailKey = 'auth_email';
  static const String passwordKey = 'auth_password';
  static const String mobileKey = 'auth_mobile';
  static const String themeKey = 'app_theme';
  static const String buildMessagesKey = 'build_messages';
  static const String chatMessagesKey = 'chat_messages';
  static const String transactionHistoryKey = 'transaction_history';
  static const String workflowInputsKey = 'workflow_inputs';

  // Validation Patterns
  static final RegExp emailPattern =
      RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  static final RegExp mobilePattern = RegExp(r'^\d{10}$');
  static final RegExp urlPattern =
      RegExp(r'^(http|https)://[a-zA-Z0-9\-.]+\.[a-zA-Z]{2,}(/[\w\-./]*)*$');

  // Minimum Requirements
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;

  // API Timeouts
  static const Duration connectionTimeout = Duration(minutes: 5);
  static const Duration receiveTimeout = Duration(minutes: 5);
  static const Duration sendTimeout = Duration(minutes: 5);

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Layout Constants
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 860; // Changed from 1200
  static const double desktopBreakpoint = 1440;

  // Default Padding and Margins
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // Border Radius
  static const double smallBorderRadius = 4.0;
  static const double defaultBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Font Families
  static const String primaryFontFamily = 'SFProText';
  static const String secondaryFontFamily = 'TestTiemposText';

  // Error Messages
  static const String genericErrorMessage = 'An unexpected error occurred';
  static const String connectionErrorMessage =
      'Please check your internet connection';
  static const String authErrorMessage = 'Authentication failed';

  // Routes
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String homeRoute = '/home';
  static const String chatRoute = '/chat';
  static const String buildRoute = '/create';
  static const String buildNewRoute = '/build_new';
  static const String settingsRoute = '/settings';
  static const String profileRoute = '/profile';
  static const String transactionRoute = '/transact';
  static const String myTransactionsRoute = '/my_transactions';
  static const String workflowRoute = '/workflow';
  static const String workflowView = '/workflow_view';
  static const String componentsRoute = '/components';
  static const String widgetBinderRoute = '/widget_binder';

  static List<String> getRoles() {
    return <String>['Administrator', 'User', 'Manager'];
  }
}
