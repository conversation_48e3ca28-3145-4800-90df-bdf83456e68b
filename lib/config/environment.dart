import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'app_config.dart';
import '../utils/logger.dart';

/// A class that provides access to environment variables and API endpoints
class Environment {
  // Private constructor to prevent instantiation
  Environment._();

  // Base URLs
  static String get transactionApiBaseUrl => AppConfig.transactionBaseUrl;
  static String get buildApiBaseUrl => AppConfig.buildBaseUrl;
  static String get chatApiBaseUrl => AppConfig.chatBaseUrl;
  static String get authApiBaseUrl => AppConfig.authBaseUrl;
  static String get workflowApiBaseUrl => AppConfig.workflowBaseUrl;

  // Transaction API Endpoints
  static String get transactionsUrl =>
      '$transactionApiBaseUrl/api/transactions';
  static String get executeUrl => '$transactionApiBaseUrl/api/execute';
  static String get globalObjectivesUrl =>
      '$transactionApiBaseUrl/api/v1/global-objectives/';
  static String get transactionDetailsUrl =>
      '$transactionApiBaseUrl/api/v1/global-objectives/transactionsdetails';

  // Workflow API Endpoints
  static String get workflowInstancesUrl =>
      '$workflowApiBaseUrl/api/v1/workflows/instances';
  static String workflowInstanceUrl(String instanceId) =>
      '$workflowInstancesUrl/$instanceId';
  static String workflowStartUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/start';
  static String workflowExecuteUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/execute';
  static String workflowInputsUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/inputs';

  // Build API Endpoints
  static String get buildChatUrl => '$buildApiBaseUrl/api/chat';
  static String get solutionsUrl => '$buildApiBaseUrl/api/solutions';
  static String get deployUrl => '$buildApiBaseUrl/api/deploy_by_id';

  // Chat API Endpoints
  static String get chatCompletionsUrl => '$chatApiBaseUrl/chat/completions';

  // Auth API Endpoints
  // static String get loginUrl => '$authApiBaseUrl/api/v1/auth/auth/token';
  static String get loginUrl => '$authApiBaseUrl/api/v2/auth/login';

  static String get registerUrl => '$authApiBaseUrl/api/v1/auth/auth/register';
  static String get logoutUrl => '$authApiBaseUrl/api/v1/auth/auth/logout';
  static String get refreshTokenUrl =>
      '$authApiBaseUrl/api/v1/auth/auth/refresh';
  static String get userProfileUrl => '$authApiBaseUrl/api/v1/auth/auth/me';

  // API Keys
  static String? get openAiApiKey => dotenv.env['API_KEY'];

  /// Initialize the environment
  static Future<void> init() async {
    try {
      Logger.info('Initializing Environment');

      // Load environment variables
      await AppConfig.init();

      Logger.info('Environment initialized successfully');
    } catch (e) {
      Logger.error('Error initializing Environment: $e');
      rethrow;
    }
  }
}
